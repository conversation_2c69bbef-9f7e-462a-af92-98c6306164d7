#%%
from itertools import count

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from PIL import Image
import matplotlib.pyplot as plt
import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"  # 允许重复加载OpenMP
#%%
# 定义简单的CNN模型
import numpy as np
class EdgeDetectionCNN(nn.Module):
    def __init__(self):
        super(EdgeDetectionCNN, self).__init__()
        # 使用固定的边缘检测卷积核
        self.conv1 = nn.Conv2d(1, 2, kernel_size=3, padding=1, bias=False)

        # 手动设置卷积核权重（水平和垂直边缘检测）
        sobel_x = torch.tensor([[[[-1, 0, 1],
                                  [-2, 0, 2],
                                  [-1, 0, 1]]]], dtype=torch.float32)

        sobel_y = torch.tensor([[[[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]]], dtype=torch.float32)

        # 组合两个卷积核
        edge_kernels = torch.cat([sobel_x, sobel_y], dim=0)  #dim = 0: 在第0维度上拼接
        print(edge_kernels)
        self.conv1.weight = nn.Parameter(edge_kernels, requires_grad=False)

    def forward(self, x):
        # 应用边缘检测卷积
        edge_features = self.conv1(x)
        # 分离水平和垂直特征
        horizontal = edge_features[:, 0:1, :, :]
        vertical = edge_features[:, 1:2, :, :]
        # 计算边缘强度
        edge_magnitude = torch.sqrt(horizontal ** 2 + vertical ** 2)
        return edge_magnitude, horizontal, vertical


# 图像预处理
def preprocess_image(image_path):
    # 打开图像并转换为灰度
    image = Image.open(image_path).convert('L') # convert: L: 灰度图, RGB: 彩色图
    # 定义图像预处理操作
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5], std=[0.5])
    ])
    # 添加batch维度 [1, 1, H, W]
    image_tensor = transform(image).unsqueeze(0) # unsqueeze: 在指定位置添加一个维度
    return image_tensor, image


# 可视化结果
def visualize_results(original, horizontal, vertical, magnitude):
    plt.figure(figsize=(12, 10))

    # 原始图像
    plt.subplot(2, 2, 1)
    plt.imshow(original, cmap='gray')
    # plt.title('Original Image')
    plt.axis('off')

    # # 水平边缘
    # plt.subplot(2, 2, 2)
    # plt.imshow(horizontal, cmap='gray')
    # plt.title('Horizontal Edges')
    # plt.axis('off')
    #
    # # 垂直边缘
    # plt.subplot(2, 2, 3)
    # plt.imshow(vertical, cmap='gray')
    # plt.title('Vertical Edges')
    # plt.axis('off')
    #
    # # 边缘强度
    # plt.subplot(2, 2, 4)
    # plt.imshow(magnitude, cmap='gray')
    # plt.title('Edge Magnitude')
    # plt.axis('off')

    plt.tight_layout()
    plt.savefig('edge_detection_result.png')
    plt.show()


# 主流程
if __name__ == "__main__":
    # 1. 初始化模型
    model = EdgeDetectionCNN()

    # 2. 加载和预处理图像
    image_tensor, original_image = preprocess_image(r'D:\python\AI250301\deeplearning\torch_1\终局.jpeg')  # 替换为你的图片路径

    # 3. 提取边缘特征
    with torch.no_grad():
        edge_magnitude, horizontal, vertical = model(image_tensor)

    # 4. 转换为numpy并后处理
    horizontal_np = horizontal.squeeze().numpy()
    vertical_np = vertical.squeeze().numpy()
    magnitude_np = edge_magnitude.squeeze().numpy()

    # 5. 可视化结果
    visualize_results(original_image,
                      horizontal_np,
                      vertical_np,
                      magnitude_np)
#%%
# 创建模型
model = nn.Sequential(nn.Linear(2, 3), nn.ReLU(), nn.Linear(3, 1))

# 注册一个前向钩子
def print_hook(module, input, output):
    print(f"钩子触发: {module.__class__.__name__} 输出形状: {output.shape}")
    return output
444
# 为注册钩子
model.register_forward_hook(print_hook)

# 测试数据
x = torch.randn(1, 2)

print("=== 使用 model(x) 调用 ===")
out1 = model(x)  # 会触发钩子

print("\n=== 使用 model.forward(x) 调用 ===")
out2 = model.forward(x)  # 不会触发钩子
#%%
import torch
import torch.nn as nn

# 定义张量x，它的尺寸是1×1×28×28
# 表示了1个，单通道，32×32大小的数据
x = torch.zeros([1, 1, 32, 32])
# 定义一个输入通道是1，输出通道是6，卷积核大小是5x5的卷积层
conv1 = nn.Conv2d(in_channels=1, out_channels=6, kernel_size=5)
# 将x，输入至conv，计算出结果c
c1 = conv1(x)
# 打印结果尺寸程序输出：
print(c1.shape)

# 定义最大池化层
pool = nn.MaxPool2d(2)
# 将卷积层计算得到的特征图c，输入至pool
s1 = pool(c1)
# 输出s的尺寸
print(s1.shape)

# 定义第二个输入通道是6，输出通道是16，卷积核大小是5x5的卷积层
conv2 = nn.Conv2d(in_channels=6, out_channels=16, kernel_size=5)
# 将x，输入至conv，计算出结果c
c2 = conv2(s1)
# 打印结果尺寸程序输出：
print(c2.shape)

s2 = pool(c2)
# 输出s的尺寸
print(s2.shape)
#%%
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torchvision import datasets, transforms
import time
from matplotlib import pyplot as plt

pipline_train = transforms.Compose([
    # 随机旋转图片
    # MNIST 是手写数字数据集，左右翻转可能造成语义错误（例如，6 和 9 会被混淆）。所以不建议使用
    # transforms.RandomHorizontalFlip(),
    # 将图片尺寸resize到32x32
    transforms.Resize((32, 32)),
    # 将图片转化为Tensor格式
    transforms.ToTensor(),
    # 正则化(当模型出现过拟合的情况时，用来降低模型的复杂度)
    transforms.Normalize((0.1307,), (0.3081,))
])
pipline_test = transforms.Compose([
    # 将图片尺寸resize到32x32
    transforms.Resize((32, 32)),
    transforms.ToTensor(),
    transforms.Normalize((0.1307,), (0.3081,))
])
# 下载数据集
train_set = datasets.MNIST(root="./dataset", train=True, download=True, transform=pipline_train)
test_set = datasets.MNIST(root="./da"
                               "taset", train=False, download=True, transform=pipline_test)
# 加载数据集
trainloader = torch.utils.data.DataLoader(train_set, batch_size=64, shuffle=True)
testloader = torch.utils.data.DataLoader(test_set, batch_size=32, shuffle=False)


# 构建LeNet模型
class LeNet(nn.Module):
    def __init__(self):
        super(LeNet, self).__init__()
        self.conv1 = nn.Conv2d(1, 6, 5)
        self.relu = nn.ReLU()
        self.maxpool1 = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        self.maxpool2 = nn.MaxPool2d(2, 2)
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 10)

    def forward(self, x):
        # 对输入进行卷积操作
        x = self.conv1(x)
        # 对卷积结果进行激活函数操作
        x = self.relu(x)
        # 对激活结果进行最大池化操作
        x = self.maxpool1(x)
        # 对最大池化结果进行卷积操作
        x = self.conv2(x)
        # 对卷积结果进行最大池化操作
        x = self.maxpool2(x)
        # 将最大池化结果展平成一维向量
        x = x.view(-1, 16 * 5 * 5)
        # 对展平结果进行全连接层操作
        x = F.relu(self.fc1(x))
        # 对全连接层结果进行激活函数操作
        x = F.relu(self.fc2(x))
        # 对激活结果进行全连接层操作
        x = self.fc3(x)
        # 返回最终结果
        return x


# 创建模型，部署gpu
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = LeNet().to(device)

#%%
# 定义优化器
optimizer = optim.Adam(model.parameters(), lr=0.001)

def train_runner(model, device, trainloader, optimizer, epoch):
    model.train()
    total_loss = 0
    total_correct = 0
    total_samples = 0

    for i, (inputs, labels) in enumerate(trainloader):
        inputs, labels = inputs.to(device), labels.to(device)
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = F.cross_entropy(outputs, labels)
        predict = outputs.argmax(dim=1) # 取出最大值对应的索引，dim=1表示在行上取最大值，因为 outputs 的形状是 [batch_size, num_classes]， 所以取出的索引就是预测的类别
        correct = (predict == labels).sum().item() # 计算预测正确的数量，sum().item()表示将 tensor 转换为 python 的数值类型

        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        total_correct += correct
        total_samples += labels.size(0) # labels.size(0) 0表示 batch_size

        if i % 100 == 0:
            print(f"Epoch {epoch}, Batch {i}, Loss: {loss.item():.6f}, Accuracy: {correct / labels.size(0) * 100:.2f}%")

    avg_loss = total_loss / len(trainloader)
    avg_acc = total_correct / total_samples
    print(f"Epoch {epoch} - Average Loss: {avg_loss:.6f}, Accuracy: {avg_acc * 100:.2f}%")
    return avg_loss, avg_acc


def test_runner(model, device, testloader):
    # 模型验证, 必须要写, 否则只要有输入数据, 即使不训练, 它也会改变权值
    # 因为调用eval()将不启用 BatchNormalization 和 Dropout, BatchNormalization和Dropout置为False
    model.eval()
    # 统计模型正确率, 设置初始值
    correct = 0.0
    test_loss = 0.0
    total = 0
    # torch.no_grad将不会计算梯度, 也不会进行反向传播
    with torch.no_grad():
        for data, label in testloader:
            data, label = data.to(device), label.to(device)
            output = model(data)
            test_loss += F.cross_entropy(output, label).item()
            predict = output.argmax(dim=1)
            # 计算正确数量
            total += label.size(0)
            correct += (predict == label).sum().item()
        # 计算损失值
        print("test_avarage_loss: {:.6f}, accuracy: {:.6f}%".format(test_loss / total, 100 * (correct / total)))


# 调用
epoch = 5
Loss = []
Accuracy = []
for epoch in range(1, epoch + 1):
    print("start_time", time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    loss, acc = train_runner(model, device, trainloader, optimizer, epoch)
    Loss.append(loss)
    Accuracy.append(acc)
    test_runner(model, device, testloader)
    print("end_time: ", time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())), '\n')

print('Finished Training')
# 保存模型
torch.save(model.state_dict(), './mymodel.pt')
# 绘制损失函数和准确率曲线

plt.figure(figsize=(10, 5))
plt.subplot(1, 2, 1)
plt.plot(Loss)
plt.title('Training Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')

plt.subplot(1, 2, 2)
plt.plot(Accuracy)
plt.title('Training Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.tight_layout()
plt.show()
#%%
import random
model.load_state_dict(torch.load('./mymodel.pt'))
print("成功加载模型....")

index = random.randint(0,1000)
image, label = train_set[index]  # 从 test_set 中直接获取图像和标签
image = image.unsqueeze(0).to(device)

# 进行预测
model.eval()
with torch.no_grad():
    output = model(image)
    predicted_label = output.argmax(dim=1, keepdim=True)

print("Predicted label:", predicted_label[0].item())
print("Actual label:", label)
# 使用图形可视化
plt.imshow(image.squeeze().cpu().numpy(), cmap='gray')
# 比较预测图和真实图
plt.title(f"Predicted labels: {predicted_label[0].item()}")
plt.show()
# 使用图形可视化
plt.imshow(image.squeeze().cpu().numpy(), cmap='gray')
plt.title(f"Actual labels: {label}")
plt.show()


#%%
import numpy as np
import matplotlib.pyplot as plt

# 模拟数据
X_positive = np.array([[0.2, 0.8], [0.4, 0.6]])  # 全正值输入
X_centered = np.array([[-0.3, 0.5], [0.1, 0.2]]) # 零中心输入
y = np.array([1, 1])

# 权重初始化
w_pos = np.array([0.1, 0.1])
w_cen = np.array([0.1, 0.1])

# 存储路径
path_pos = [w_pos.copy()]
path_cen = [w_cen.copy()]

# 训练过程
for _ in range(100):
    # 全正值数据更新
    y_pred = X_positive @ w_pos
    grad = (y_pred - y) @ X_positive
    w_pos -= 0.01 * grad
    path_pos.append(w_pos.copy())

    # 零中心数据更新
    y_pred = X_centered @ w_cen
    grad = (y_pred - y) @ X_centered
    w_cen -= 0.01 * grad
    path_cen.append(w_cen.copy())

# 可视化
path_pos = np.array(path_pos)
path_cen = np.array(path_cen)

plt.figure(figsize=(12,6))
plt.subplot(121)
plt.plot(path_pos[:,0], path_pos[:,1], 'o-')
plt.title("全正值输入: Z字下降")
plt.xlabel("w1"); plt.ylabel("w2")

plt.subplot(122)
plt.plot(path_cen[:,0], path_cen[:,1], 'o-')
plt.title("零中心输入: 直接收敛")
plt.xlabel("w1"); plt.ylabel("w2")

plt.tight_layout()
plt.show()
#%%
def findTheDifference(s, t):
    words= {}
    sw = []
    tw = []
    for i in range(len(s)):
        sw.append(s[i])
    for j in range(len(t)):
        tw.append(t[j])
    sw = sorted(sw)
    tw = sorted(tw)
    for ind,word in enumerate(tw):
        words[word]=tw.count(tw[ind])
    for ind,word in enumerate(sw):
        words[word]+=sw.count(sw[ind])
    print(words)
    for i in words:
        if words[i]%2!=0:
            print(words[i])
            return i
findTheDifference("abed","abcde")
#%%
n = [1,2,4,5,6,67,5,43,2,22,2,2,2]
