
# 使用全連接網絡訓練和預測MinST數據集
# 1.數據准備
# 2.構建網絡結構
# 3.實現訓練方法:交叉熵損失函數,ADM優化器
# 4.實現驗證方法
# 4.通過測試圖片進行預測
def build_data():
    transform = transforms.Compose([
        transforms.Resize((28,28)),
        transforms.ToTensor(),
    ])
    train_datasets = datasets.MNIST(root='./data',
                                    train=True,
                                    transform= transform,
                                    download=True)
    val_datasets = datasets.MNIST(root='./data',
                                    train=False,
                                    transform= transform,
                                    download=True)
    train_loader = DataLoader(
        dataset = train_datasets,
        batch_size = 32,
        shuffle = True
    )
    val_loader = DataLoader(
        dataset = val_datasets,
        batch_size = 32,
        shuffle = True
    )
    return train_loader, val_loader
# 1.構建網絡結構
class MiNsTNet(nn.Module):
    def __init__(self,in_features,out_features):
        super().__init__()
        self.fc1 =nn.Linear(in_features,128)
        self.bn1 = nn.BatchNorm1d(128)
        self.relu1 = nn.ReLU()

        self.fc2 = nn.Linear(128,64)
        self.bn2 = nn.BatchNorm1d(64)
        self.relu2 = nn.ReLU()

        self.fc3 =nn.Linear(64,out_features)
    def forward(self,x):
        # 将输入x展平成一维向量
        x = x.view(-1,1*28*28)
        # 将x输入到第一个全连接层，经过批归一化和ReLU激活函数
        x = self.relu1(self.bn1(self.fc1(x)))
        # 将x输入到第二个全连接层，经过批归一化和ReLU激活函数
        x = self.relu2(self.bn2(self.fc2(x)))
        # 将x输入到第三个全连接层
        x = self.fc3(x)
        # 返回输出
        return x
# 2.實現訓練方法:交叉熵損失函數,ADM優化器
def train(model,train_dataloader,lr,epoches):
    criterion = nn.CrossEntropyLoss()
    opt = optim.Adam(model.parameters(),lr =lr,betas= (0.9,0.999),weight_decay=0.001)
    #betas是Adam算法中的矩估计的指数衰减率，作用是 控制梯度下降的步長，
    # lr作用是控制梯度下降的步長，
    for epoch in range(epoches):
        correct = 0
        for tx,ty in train_dataloader:
            y_pred = model(tx)
            loss = criterion(y_pred,ty)
            opt.zero_grad()
            loss.backward()
            opt.step()

            _,pred =  torch.max(y_pred.data,dim= 1)
            correct += (pred == ty).sum().item()

        print('epoch:{}'.format(epoch))
def eval(model,val_dataloader):
    model.eval()

    criterion = nn.CrossEntropyLoss()
    correct = 0
    for vx,vy in val_dataloader:
        with torch.no_grad():
            y_pred = model(vx)
            loss = criterion(y_pred,vy)
            _,pred = torch.max(y_pred.data,dim=1)
            correct +=(pred ==vy).sum().item()
    print(f'loss:{loss.item()}')

def save_model(model,path):
    torch.save(model.state_dict(),path)
def load_model(path):
    model = MiNsTNet(1*28*28,10)
    model.load_state_dict(torch.load(path))
    return model
# 4.通過測試圖片進行預測
def predict(test_path,model_path):
    transform = transforms.Compose([
        transforms.Resize((28,28)),
        transforms.ToTensor()
    ])
    img = Image.open(test_path).convert('L')
    t_img = transform(img).unsqueeze(0)

    model = load_model(model_path)
    model.eval()
    with torch.no_grad():
        y_pred = model(t_img)
        _,pred = torch.max(y_pred.data,dim=1)
        print(f'預測分類：{pred.item()}')
if __name__ == '__main__':
    train_dataloader,val_dataloader = build_data()
    model = MiNsTNet(1*28*28,10)
    # train(model,train_dataloader,lr=0.001,epoches=20)
    eval(model,val_dataloader)
    save_model(model,'./minst.pt')
    predict('./img/3.png','./minst.pt')


