import os
import sys
import json
import torch
import torch.nn as nn
from torchvision import transforms, datasets
import torch.optim as optim
from tqdm import tqdm
import tarfile
import random
import shutil
from Mymodel import AlexNet

# ******************** 新增数据准备函数 ********************
def prepare_data_from_tgz(tgz_path, data_root, split_ratio=0.8):
    """
    解压 tgz 文件并按比例分割数据集为训练集和验证集
    """
    extract_path = os.path.join(data_root, "flower_photos")
    image_path = os.path.join(data_root, "flower_data")
    train_path = os.path.join(image_path, "train")
    val_path = os.path.join(image_path, "val")

    # 如果数据集已经准备好，则跳过
    if os.path.exists(train_path) and os.path.exists(val_path):
        print(f"'{train_path}' and '{val_path}' already exist. Skipping data preparation.")
        return image_path

    # 1. 解压缩 tgz 文件
    if not os.path.exists(extract_path):
        print(f"Extracting '{tgz_path}'...")
        with tarfile.open(tgz_path, 'r:gz') as tar:
            tar.extractall(path=data_root)
    else:
        print(f"'{extract_path}' already exists. Skipping extraction.")

    # 2. 创建 train 和 val 目录
    os.makedirs(train_path, exist_ok=True)
    os.makedirs(val_path, exist_ok=True)

    # 3. 分割数据集
    print("Splitting dataset into train and validation sets...")
    classes = [d for d in os.listdir(extract_path) if os.path.isdir(os.path.join(extract_path, d))]

    for cls in classes:
        # 在 train 和 val 目录下创建类别子目录
        os.makedirs(os.path.join(train_path, cls), exist_ok=True)
        os.makedirs(os.path.join(val_path, cls), exist_ok=True)

        # 获取所有图片路径
        cls_path = os.path.join(extract_path, cls)
        images = [f for f in os.listdir(cls_path) if f.endswith('.jpg')]
        random.shuffle(images)

        # 分割点
        split_point = int(len(images) * split_ratio)
        train_images = images[:split_point]
        val_images = images[split_point:]

        # 移动图片文件
        for img in train_images:
            shutil.move(os.path.join(cls_path, img), os.path.join(train_path, cls, img))
        for img in val_images:
            shutil.move(os.path.join(cls_path, img), os.path.join(val_path, cls, img))

    print("Data preparation finished.")
    # 清理空的原始解压文件夹
    try:
        shutil.rmtree(extract_path)
        print(f"Cleaned up temporary directory: '{extract_path}'")
    except OSError as e:
        print(f"Error cleaning up directory {extract_path}: {e}")

    return image_path


# **********************************************************


def main():
    # 使用GPU
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print("using {} device.".format(device))

    # ******************** 修改数据准备逻辑 ********************
    # 假设 flower_photos.tgz 在项目根目录
    tgz_file_path = 'flower_photos.tgz'
    data_root_path = os.path.abspath(os.path.join(os.getcwd(), "data_set"))

    if not os.path.exists(tgz_file_path):
        print(f"Error: '{tgz_file_path}' not found! Please place it in the project root directory.")
        sys.exit(1)

    # 调用新函数准备数据
    image_path = prepare_data_from_tgz(tgz_file_path, data_root_path, split_ratio=0.8)
    # **********************************************************

    data_transform = {
        "train":
            transforms.Compose([
                transforms.RandomResizedCrop(224),
                transforms.RandomHorizontalFlip(),
                transforms.RandomRotation(30),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.ToTensor(),
                transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
            ]),
        "val":
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
            ])
    }

    train_dataset = datasets.ImageFolder(root=os.path.join(image_path, "train"),
                                         transform=data_transform["train"])
    train_num = len(train_dataset)

    flower_list = train_dataset.class_to_idx
    cla_dict = dict((val, key) for key, val in flower_list.items())

    json_str = json.dumps(cla_dict, indent=4)
    with open('class_indices.json', 'w') as json_file:
        json_file.write(json_str)

    batch_size = 32

    train_loader = torch.utils.data.DataLoader(train_dataset,
                                               batch_size=batch_size,
                                               shuffle=True,
                                               num_workers=0)

    validate_dataset = datasets.ImageFolder(root=os.path.join(image_path, "val"),
                                            transform=data_transform["val"])
    val_num = len(validate_dataset)
    validate_loader = torch.utils.data.DataLoader(validate_dataset,
                                                  batch_size=4,
                                                  shuffle=False,
                                                  num_workers=0)

    print("using {} images for training, {} images for validation.".format(
        train_num, val_num))

    net = AlexNet(num_classes=len(flower_list), init_weights=True)

    net.to(device)
    loss_function = nn.CrossEntropyLoss()
    optimizer = optim.Adam(net.parameters(), lr=0.0002)

    epochs = 50
    save_path = './AlexNet.pth'
    best_acc = 0.0
    train_steps = len(train_loader)

    for epoch in range(epochs):
        # train
        net.train()
        running_loss = 0.0
        train_bar = tqdm(train_loader, file=sys.stdout)
        for step, data in enumerate(train_bar):
            images, labels = data
            optimizer.zero_grad()
            outputs = net(images.to(device))
            loss = loss_function(outputs, labels.to(device))
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

            train_bar.desc = "train epoch[{}/{}] loss:{:.3f}".format(
                epoch + 1, epochs, loss)

        # validate
        net.eval()
        acc = 0.0
        with torch.no_grad():
            val_bar = tqdm(validate_loader, file=sys.stdout)
            for val_data in val_bar:
                val_images, val_labels = val_data
                outputs = net(val_images.to(device))
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels.to(device)).sum().item()

        val_accurate = acc / val_num
        print('[epoch %d] train_loss: %.3f  val_accuracy: %.3f' %
              (epoch + 1, running_loss / train_steps, val_accurate))

        if val_accurate > best_acc:
            best_acc = val_accurate
            torch.save(net.state_dict(), save_path)

    print('Finished Training')


if __name__ == '__main__':
    main()