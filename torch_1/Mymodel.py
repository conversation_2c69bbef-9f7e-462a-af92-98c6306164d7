import torch.nn as nn
import torch


class AlexNet(nn.Module):
    def __init__(self, num_classes=1000, init_weights=False):
        # 通过初始化函数来定义网络在正向传播过程中所需要使用到的层结构
        super(AlexNet, self).__init__()
        self.features = nn.Sequential(
            # 使用这个nn.Sequential能将一系列层结构打包，features用于提取图像特征
            # 不用每次写self.nn. 数据集比较小，卷积核的个数使用AlexNet中的一半
            nn.Conv2d(3, 48, kernel_size=11, stride=4, padding=2),  # input[3, 224, 224]  output[48, 55, 55]
            # padding一般只能传入int整型和tuple类型，比如tuple(1,2)代表上下各补一行0，左右各2列0；
            # 按照论文中精确padding，用nn.ZeroPad2d((1,2,1,2)),左1右2上1下2
            # pytorch卷积和池化过程中如果输出不是整数，会自动把多余数据舍弃掉
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2),  # output[48, 27, 27]
            nn.Conv2d(48, 128, kernel_size=5, padding=2),  # output[128, 27, 27] 步长为1直接默认
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2),  # output[128, 13, 13]
            nn.Conv2d(128, 192, kernel_size=3, padding=1),  # output[192, 13, 13]
            nn.ReLU(inplace=True),
            nn.Conv2d(192, 192, kernel_size=3, padding=1),  # output[192, 13, 13]
            nn.ReLU(inplace=True),
            nn.Conv2d(192, 128, kernel_size=3, padding=1),  # output[128, 13, 13]
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2),  # output[128, 6, 6]
        )
        # 包含全连接层  卷积层之后别忘了展平
        # 分开写是为了dropout只用于fc层
        self.classifier = nn.Sequential(
            nn.Dropout(p=0.5),  # 以0.5的概率随机失活一些节点
            nn.Linear(128 * 6 * 6, 2048),
            nn.ReLU(inplace=True),
            nn.Dropout(p=0.5),
            nn.Linear(2048, 2048),
            nn.ReLU(inplace=True),
            nn.Linear(2048, num_classes),
        )
        if init_weights:  # 初始化权重
            self._initialize_weights()

    # 正向传播
    def forward(self, x):
        x = self.features(x)  # 进入卷积部分
        x = torch.flatten(x, start_dim=1)  # 展平处理，从第1维度开始展平，batch维度不动，也可以用view函数
        x = self.classifier(x)  # 进入全连接层
        return x

    # 初始化权重函数
    def _initialize_weights(self):
        for m in self.modules():  # 会遍历我们定义的每一个层结构
            if isinstance(m, nn.Conv2d):  # 如果层结构是卷积层
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                # 使用这个初始化变量方法来对我们卷积变量w进行初始化
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)  # 对偏置进行初始化
            elif isinstance(m, nn.Linear):  # 如果是全连接层
                nn.init.normal_(m.weight, 0, 0.01)  # 使用正态分布来初始化
                nn.init.constant_(m.bias, 0)  # 偏置初始化为0