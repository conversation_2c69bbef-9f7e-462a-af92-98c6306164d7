import torch.nn as nn
import torch.optim as optim

import torch

def modlr():
    module = nn.Linear(40,50)
    celoss = nn.MSELoss()
    ceti = optim.SGD(module.parameters(),lr=0.9)
    input = torch.randn(50,40)
    output = module(input)
    ceti.zero_grad()
    loss = celoss(output,torch.randn(50,50))
    loss.backward()
    ceti.step()
    print(loss.item())
modlr()
class Mydaraset(torch.utils.data.Dataset):
    def __init__(self,x,y):
        self.x = x
        self.y = y
    def __getitem__(self, item):
        return self.x[item],self.y[item]
    def __len__(self):
        return len(self.x)

def test01():
    x = torch.randn(100,20)
    y= torch.randn(100,1)
    dataset = Mydaraset(x,y)
    dataloader = torch.utils.data.DataLoader(dataset,batch_size=50,shuffle=True)
    for i in dataloader:
        print(i)
    return dataloader

test01()