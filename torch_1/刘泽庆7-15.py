import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np


# 定义简单的CNN模型
class EdgeDetectionCNN(nn.Module):
    def __init__(self):
        super(EdgeDetectionCNN, self).__init__()
        # 使用固定的边缘检测卷积核
        self.conv1 = nn.Conv2d(1, 2, kernel_size=3, padding=1, bias=False)

        # 手动设置卷积核权重（水平和垂直边缘检测）
        sobel_x = torch.tensor([[[[-1, 0, 1],
                                  [-2, 0, 2],
                                  [-1, 0, 1]]]], dtype=torch.float32)

        sobel_y = torch.tensor([[[[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]]], dtype=torch.float32)

        # 组合两个卷积核
        edge_kernels = torch.cat([sobel_x, sobel_y], dim=0)
        self.conv1.weight = nn.Parameter(edge_kernels, requires_grad=False)

    def forward(self, x):
        # 应用边缘检测卷积
        edge_features = self.conv1(x)
        # 分离水平和垂直特征
        horizontal = edge_features[:, 0:1, :, :]
        vertical = edge_features[:, 1:2, :, :]
        # 计算边缘强度
        edge_magnitude = torch.sqrt(horizontal ** 2 + vertical ** 2)
        return edge_magnitude, horizontal, vertical


# 图像预处理
def preprocess_image(image_path):
    # 打开图像并转换为灰度
    image = Image.open(image_path).convert('L')
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5], std=[0.5])
    ])
    # 添加batch维度 [1, 1, H, W]
    image_tensor = transform(image).unsqueeze(0)
    return image_tensor, image


# 可视化结果
def visualize_results(original, horizontal, vertical, magnitude):
    plt.figure(figsize=(12, 10))

    # 原始图像
    plt.subplot(2, 2, 1)
    plt.imshow(original, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')

    # 水平边缘
    plt.subplot(2, 2, 2)
    plt.imshow(horizontal, cmap='gray')
    plt.title('Horizontal Edges')
    plt.axis('off')

    # 垂直边缘
    plt.subplot(2, 2, 3)
    plt.imshow(vertical, cmap='gray')
    plt.title('Vertical Edges')
    plt.axis('off')

    # 边缘强度
    plt.subplot(2, 2, 4)
    plt.imshow(magnitude, cmap='gray')
    plt.title('Edge Magnitude')
    plt.axis('off')

    plt.tight_layout()
    plt.savefig('edge_detection_result.png')
    plt.show()


# 主流程
if __name__ == "__main__":
    # 1. 初始化模型
    model = EdgeDetectionCNN()

    # 2. 加载和预处理图像
    image_tensor, original_image = preprocess_image(r'/\torch_1\15.jpg')  # 替换为你的图片路径

    # 3. 提取边缘特征
    with torch.no_grad():
        edge_magnitude, horizontal, vertical = model(image_tensor)

    # 4. 转换为numpy并后处理
    horizontal_np = horizontal.squeeze().numpy()
    vertical_np = vertical.squeeze().numpy()
    magnitude_np = edge_magnitude.squeeze().numpy()

    # 5. 可视化结果
    visualize_results(original_image,
                      horizontal_np,
                      vertical_np,
                      magnitude_np)