# import urllib.request,http.cookiejar
# import urllib.error
# from urllib.parse import urlparse, unquote
#
# filename = 'cookie.txt'
# # cookie = http.cookiejar.CookieJar()
# # cookie = http.cookiejar.MozillaCookieJar(filename)
# cookie = http.cookiejar.LWPCookieJar(filename)
# # cookie.save(filename,ignore_discard=True,ignore_expires=True)
# #将保存在内存中的cookie保存到文件中，ignore_expires：忽略掉已经过期的cookie
# # ignore_discard：如果cookie被浏览器设置为丢弃，那么这个cookie就不会被保存到文件中
# cookie.load(filename,ignore_discard=True,ignore_expires=True)
# handler = urllib.request.HTTPCookieProcessor(cookie)
# opener = urllib.request.build_opener(handler)
# url = 'https://www.jd.com/?cu=true&utm_source=lianmeng__10__ntp.msn.cn&utm_medium=tuiguang&utm_campaign=t_2030767747_&utm_term=87a4116c120b4ccca7704642c4323481'
# result = urlparse(url)
# try:
#     response = opener.open('http://www.baidu.com')
#     print(result)
#     print(response.status)
#     print(response.getheaders(), "\n")
#     # print(response.read().decode('utf-8'))
# except urllib.error.HTTPError as e:
#     print(e.reason,e.code,e.headers,sep="\n") #sep: 分隔符
#
import requests


