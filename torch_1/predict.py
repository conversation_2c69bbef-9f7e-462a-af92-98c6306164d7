import os
import json
import torch
from PIL import Image
from torchvision import transforms
import matplotlib.pyplot as plt
from Mymodel import AlexNet
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"


def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    data_transform = transforms.Compose(  # 依然是对数据先进行预处理
        [transforms.Resize((224, 224)),
         transforms.ToTensor(),
         transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))])

    # load image
    img_path = r"D:\python\AI250301\deeplearning\torch_1\dataset\flower_photos\daisy\5547758_eea9edfd54_n.jpg"
    assert os.path.exists(img_path), "file: '{}' dose not exist.".format(img_path)
    img = Image.open(img_path)  # 直接使用PIL库载入一张图像

    plt.imshow(img)  # 简单展示一下这张图片
    # [N, C, H, W]
    img = data_transform(img)  # 对图片进行预处理
    # expand batch dimension
    img = torch.unsqueeze(img, dim=0)  # 扩充一个维度，添加一个batch维度

    # read class_indict
    json_path = './class_indices.json'  # 读取json文件，也就是索引对应的类别名称
    assert os.path.exists(json_path), "file: '{}' dose not exist.".format(json_path)

    with open(json_path, "r") as f:
        class_indict = json.load(f)  # 对json文件进行解码，解码成我们所需要的字典

    # create model
    model = AlexNet(num_classes=5).to(device)  # 初始化我们的网络

    # load model weights
    weights_path = "./AlexNet.pth"
    assert os.path.exists(weights_path), "file: '{}' dose not exist.".format(weights_path)
    model.load_state_dict(torch.load(weights_path))  # 载入我们的网络模型

    model.eval()  # 进入eval模式，没有dropout的那个
    with torch.no_grad():  # 不跟踪变量的损失梯度
        # predict class
        output = torch.squeeze(model(img.to(device))).cpu()  # 将数据通过model进行正向传播得到输出
        # squeeze将输出进行压缩，把第一个维度的batch压缩掉了
        predict = torch.softmax(output, dim=0)  # softmax得到概率分布
        predict_cla = torch.argmax(predict).numpy()  # 概率最大处所对应的索引值

    print_res = "class: {}   prob: {:.3}".format(class_indict[str(predict_cla)],
                                                 predict[predict_cla].numpy())
    # 打印预测名称，已经对应类别的概率
    plt.title(print_res)
    for i in range(len(predict)):
        print("class: {:10}   prob: {:.3}".format(class_indict[str(i)],
                                                  predict[i].numpy()))
    plt.show()


if __name__ == '__main__':
    main()