import torch
from PIL import Image
from torchvision.transforms import ToTensor
from cnn_model import MyCNNmodel
from image_pre import preprocess_image
import time

transform = ToTensor()
model = MyCNNmodel()
model.load_state_dict(torch.load('model/Fruit_model.pth'))  # 模型路径
model.eval()
# device = torch.device('cpu')
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
model.to(device)


# 图片处理识别
def image_fruit_predict(image_path):
    start_time = time.time()
    if image_path is not None:
        preprocess_image_fruit = preprocess_image(image_path)
        print(preprocess_image_fruit)
        if preprocess_image_fruit is not None:
            img_tensor = transform(Image.fromarray(preprocess_image_fruit)).unsqueeze(0)
            predicted_class = predict(img_tensor)
        else:
            predicted_class = torch.tensor([-1])
    end_time = time.time()
    print(f'动物识别时间: {(end_time - start_time) * 1000:.2f} ms')
    return predicted_class



# 预测函数
def predict(img_tensor):
    img_tensor = img_tensor.to(device)
    with torch.no_grad():
        output = model(img_tensor)
        _, predicted_class = torch.max(output, 1)
    # print(f'Predicted Fruit Class: {predicted_class.item()}')
    return predicted_class


# 类别匹配
def map_fruit_class(predicted_class):
    Animal_mapping = {
    -1: 'None',
    0: 'bear',
    1: 'bee',
    2: 'cat',
    3: 'cow',
    4: 'deer',
    5: 'dog',
    6: 'koala',
    7: 'raccoon'
    }
    mapped_fruit_class = Animal_mapping.get(predicted_class.item())

    return mapped_fruit_class


if __name__ == "__main__":
    # 图片
    image_path = "./datasets/animal/val/bee/0fa99039ab.jpg"
    predicted_class = image_fruit_predict(image_path)
    print(predicted_class.item())
    mapped_fruit_class = map_fruit_class(predicted_class)

    print(f'Predicted animal Class: {mapped_fruit_class}')
