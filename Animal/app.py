import gradio as gr
import cv2
import gradio as gr
from myutils import cv2AddChineseText,query_fruit_nutrition
import os
from fruit_predict import image_fruit_predict, map_fruit_class
import shutil

animal_to_chinese = {
 'bear': '熊',
 'bee': '蜜蜂',
 'cat': '猫',
 'cow': '牛',
 'deer': '鹿',
 'dog': '狗',
 'koala': '考拉',
 'raccoon': '浣熊'
}

# 水果识别检查函数
def fruit_detected(src_img):
    orgin_img = cv2.imread(src_img)
    result = "未知动物"
    predicted_class = image_fruit_predict(src_img)
    print(predicted_class.item())
    mapped_fruit_class = map_fruit_class(predicted_class)
    if (mapped_fruit_class != 'None'):
        result = mapped_fruit_class
    dest_img = cv2AddChineseText(orgin_img, animal_to_chinese[result], (40, 40), (38, 223, 223), 40)
    dest_img = cv2.cvtColor(dest_img, cv2.COLOR_BGR2RGB)
    fruit_name = animal_to_chinese[result]
    nutrition = query_fruit_nutrition(fruit_name)

    result = f"动物名字:{nutrition['name']}\n"
    return dest_img, result



fruit_interface = gr.Interface(
    fn=fruit_detected,
    title='基于CNN的动物识别案例',
    inputs=[gr.Image(label='源图片', type='filepath')],
    outputs=[gr.Image(show_label=False), gr.Text(label='动物识别结果')],
    examples=[['./datasets/animal/val/deer/4d8f0a4d6f.jpg'], ['./datasets/animal/val/koala/3f1b884203.jpg'], ['./datasets/animal/val/raccoon/69b50289ff.jpg'],
              ['./datasets/animal/val/bee/45af75e0da.jpg']]
)


tabbed_interface = gr.TabbedInterface(
    [fruit_interface],
    ["水果图片检测"],
    title="xxxxx大学人工智能实训项目-基于CNN的水果动物检测识别系统"
)

if __name__ == "__main__":
    tabbed_interface.launch()
