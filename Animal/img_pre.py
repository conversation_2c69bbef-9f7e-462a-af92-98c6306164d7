import os
import shutil
import random


def prepare_data(data_dir, split_ratio=0.8):
    """
    准备数据集：创建训练集和验证集目录，并复制相应文件

    参数:
        data_dir: 解压后的数据集目录路径
        split_ratio: 训练集比例 (默认0.8)
    """
    # 创建训练集和验证集目录
    train_dir = os.path.join(data_dir, 'train')
    val_dir = os.path.join(data_dir, 'val')
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)

    # 获取所有类别
    classes = [cls for cls in os.listdir(data_dir)
               if os.path.isdir(os.path.join(data_dir, cls))
               and cls not in ('train', 'val')]

    # 分割每个类别的数据
    for cls in classes:
        cls_path = os.path.join(data_dir, cls)
        if not os.path.isdir(cls_path):
            continue

        # 创建类别子目录
        train_cls_dir = os.path.join(train_dir, cls)
        val_cls_dir = os.path.join(val_dir, cls)
        os.makedirs(train_cls_dir, exist_ok=True)
        os.makedirs(val_cls_dir, exist_ok=True)

        # 获取并打乱图像文件
        images = [img for img in os.listdir(cls_path)
                  if img.lower().endswith(('.jpg', '.jpeg', '.png'))]
        random.shuffle(images)

        # 计算分割点
        split_idx = int(len(images) * split_ratio)

        # 复制训练集图像
        for img in images[:split_idx]:
            src = os.path.join(cls_path, img)
            dst = os.path.join(train_cls_dir, img)
            shutil.copy2(src, dst)  # 保留文件元数据

        # 复制验证集图像
        for img in images[split_idx:]:
            src = os.path.join(cls_path, img)
            dst = os.path.join(val_cls_dir, img)
            shutil.copy2(src, dst)

    print(f"数据集分割完成: {len(classes)}个类别")
    print(f"训练集目录: {train_dir}")
    print(f"验证集目录: {val_dir}")

    return train_dir, val_dir

# 使用示例
if __name__ == "__main__":
    # 假设数据集解压在'./animal'目录
    data_path = 'datasets/animal'
    train_path, val_path = prepare_data(data_path, split_ratio=0.8)