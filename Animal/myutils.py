from shutil import copy
import uuid
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
import re
import json
import requests

# 生成UUID的函数
def generate_uuid():
    return str(uuid.uuid4())


# opencv实现视频里面写入中文字符串的函数
def cv2AddChineseText(img, text, position, textColor, textSize):
    if (isinstance(img, np.ndarray)):  # 判断是否OpenCV图片类型
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    # 创建一个可以在给定图像上绘图的对象
    draw = ImageDraw.Draw(img)
    # 字体的格式
    fontStyle = ImageFont.truetype(
        "simsun.ttc", textSize, encoding="utf-8")  # simsun.ttc语言包放在程序同级目录下
    # 绘制文本
    draw.text(position, text, textColor, font=fontStyle)
    # 转换回OpenCV格式
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)


# 把json字符串写入到json文件中。
"""
def writ2json(data, path):
    with open(path + '/result.json', 'w', encoding='utf-8') as file:
        # 将字符串写入文件
        file.write(data)
"""


def writ2json(data, path):
    # 确保路径存在斜杠结尾
    if not path.endswith('/'):
        path += '/'

    # 检查输入数据是字符串还是Python对象
    if isinstance(data, str):
        # 如果是字符串，解析为Python对象
        parsed_data = json.loads(data)
    else:
        # 如果是Python对象（如字典/列表），直接使用
        parsed_data = data

    # 将格式化后的JSON写入文件
    with open(path + 'result.json', 'w', encoding='utf-8') as file:
        json.dump(parsed_data, file, indent=4, ensure_ascii=False)


# 读取json文件返回json字符串
def read2json(path):
    with open(path, 'r', encoding='utf-8') as file:
        # 读取文件内容
        data = file.read()
        result_json = json.loads(data)
    return result_json


def query_fruit_nutrition(fruit_name):
    url = "https://www.simoniu.com/commons/nutrients/"
    response = requests.get(url + fruit_name)
    # print(response.text)
    jsonObj = json.loads(response.text)
    return jsonObj['data']