import gradio as gr
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from src.models.modnet import MODNet
import numpy as np
import cv2

color_classes = ['red', 'green', 'blue', 'white']
#  预处理
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Resize((512, 512)),
])

modnet = MODNet(backbone_pretrained=False)
modnet = nn.DataParallel(modnet)

# 加载模型
ckpt_path = "./pretrained/modnet_photographic_portrait_matting.ckpt"

# 加载模型
if torch.cuda.is_available():
    modnet = modnet.cuda()
    weights = torch.load(ckpt_path)
else:
    weights = torch.load(ckpt_path, map_location=torch.device('cpu'))
modnet.load_state_dict(weights)
modnet.eval()


def change_color(srcImg, color):
    # 这里做什么？
    # print(srcImg)
    print("要更换的颜色：", color)
    # 读取并预处理图像
    # original_image = cv2.imread("./images/personal02.jpg")
    # original_image = cv2.imread(srcImg)
    original_image = srcImg.astype(np.uint8)
    original_height, original_width = original_image.shape[:2]  # 获取原始高宽

    # 预处理流程（缩放到512x512）
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Resize((512, 512)),
        transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
    ])

    image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)  # 转为RGB
    # image = Resize((512, 512))(ToTensor()(image)).unsqueeze(0)  # 调整尺寸并转为Tensor
    image = transform(image).unsqueeze(0)

    # 推理
    with torch.no_grad():
        _, _, matte = modnet(image, True)

    # 后处理：生成Alpha遮罩
    matte = matte.squeeze().cpu().numpy()
    matte = cv2.resize(matte, (image.shape[3], image.shape[2]))  # 恢复原始尺寸

    # 替换背景颜色（例如：红色背景）
    new_background = np.zeros_like(image.squeeze().permute(1, 2, 0).cpu().numpy())
    match color:
        case "red":
            # 换成红色背景
            new_background[:, :] = [208, 0, 0]
        case "blue":
            # 换成蓝色背景
            new_background[:, :] = [0, 143, 213]
        case "green":
            # 换成绿色背景
            new_background[:, :] = [0, 134, 134]
        case "white":
            # 换成白色背景
            new_background[:, :] = [255, 255, 255]

    # 合成新图像
    foreground = srcImg.astype(np.uint8)
    foreground = cv2.resize(foreground, (512, 512))
    alpha = np.repeat(matte[:, :, np.newaxis], 3, axis=2)
    result = alpha * foreground + (1 - alpha) * new_background

    # 保存结果
    result = cv2.cvtColor(result.astype(np.uint8), cv2.COLOR_RGB2BGR)
    result = cv2.resize(result, (original_width, original_height))
    cv2.imwrite("result.jpg", result)

    result_img = './result.jpg'
    return result_img

# 在TabbedInterface外层包裹Blocks并添加CSS样式

# 定义更完善的蓝色主题CSS样式
custom_css = """
/* 主容器设置 */
/* 强制重置所有容器宽度 */
gradio-app > .gradio-container {
    max-width: 100% !important;
    width: 100% !important;
    min-width: 90% !important;
    margin: 0 auto !important;
    padding: 0px !important;
}

gradio-app > div{
   margin: 0px !important;
}

/* 覆盖内部容器限制 */
.contain, .blocks, .block, .panel {
    max-width: 100% !important;
    width: 100% !important;
    min-width: 100% !important;
    margin: 0px !important;
}

/* 消除所有潜在宽度限制 */
.gr-box, .gr-block, .interface {
    max-width: none !important;
    width: auto !important;
}

/* 主标题样式 */
h1 {
    font-size: 24px !important;
    color: #1565C0 !important;
    text-shadow: 2px 2px 4px rgba(25, 118, 210, 0.3) !important;
    background: linear-gradient(to right, #F0F8FF, #C6E2FF) !important;
    padding: 5px !important;
    border-radius: 12px !important;
    text-align: center !important;
    margin-top: 5px !important;
    margin-bottom: 10px !important;
}

/* 选项卡按钮基础样式 */
button.tab-button {
    background-color: #E3F2FD !important;
    color: #0D47A1 !important;
    border: 1px solid #90CAF9 !important;
    margin: 2px !important;
    padding: 10px 25px !important;
    border-radius: 5px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
}

/* 鼠标悬停效果 */
button.tab-button:hover {
    background-color: #BBDEFB !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2) !important;
}

/* 选中状态 */
button.tab-button.selected {
    background: #2196F3 !important;
    color: white !important;
    border-bottom: 3px solid #1976D2 !important;
    box-shadow: 0 4px 6px rgba(33, 150, 243, 0.4) !important;
}

/* 选项卡容器 */
div.tabs {
    background: linear-gradient(145deg, #F8F9FA, #E9ECEF) !important;
    padding: 12px !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 10px !important;
}
"""

school_name = "华清远见成都中心AI250301班"

changecolor_interface = gr.Interface(
    fn=change_color,
    title='OpenCV实现更换证件照背景测案例',
    inputs=[gr.Image(label='源图片'), gr.Dropdown(color_classes, value='red', label='背景颜色')],
    outputs=[gr.Image(show_label=False)],
    examples=[['./images/kun1.jpg', 'blue'], ['./images/kun2.jpg', 'red'],
              ['./images/kun3.jpg', 'white']]
)


with gr.Blocks(css=custom_css, theme=gr.themes.Default(primary_hue="blue")) as myapp:
    tabbed_interface = gr.TabbedInterface(
        [changecolor_interface],
        ["👨‍🦯更换证件照背景", ],
        title=(
            "<div style='text-align: center; padding: 20px; text-decoration:underline'>"
            f"🚀{school_name}人工智能实训项目<br>"
            "<div style='font-size: 0.8em; color: #1976D2; text-decoration:none !important;'>CNN卷积神经网络项目集合</div>"
            "</div>"
        )
    )

if __name__ == '__main__':
    # 定义端口号
    gradio_port = 8888
    gradio_url = f"http://127.0.0.1:{gradio_port}"
    myapp.launch(
        server_name="127.0.0.1",
        server_port=gradio_port,
        debug=True,
        # auth=("admin", "123456"),
        # auth_message="请输入账号信息访问此应用。测试账号：admin,密码：123456",
        # inbrowser=False,
        # prevent_thread_lock=True,
    )
