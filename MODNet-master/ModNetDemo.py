import gradio as gr
import os, sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from src.models.modnet import MODNet
import numpy as np
from PIL import Image

modnet = MODNet(backbone_pretrained=False)
modnet = nn.DataParallel(modnet)
ckpt_path = "./pretrained/modnet_photographic_portrait_matting.ckpt"

if torch.cuda.is_available():
    modnet = modnet.cuda()
    weights = torch.load(ckpt_path)
else:
    weights = torch.load(ckpt_path, map_location=torch.device('cpu'))
modnet.load_state_dict(weights)
modnet.eval()

#512*512
ref_size = 256
image = './images/kun2.jpg'
im = Image.open(image)
im = np.asarray(im)

if len(im.shape) == 2:
    im = im[:, :, None]
if im.shape[2] == 1:
    im = np.repeat(im, 3, axis=2)
elif im.shape[2] == 4:
    im = im[:, :, 0:3]

im_transform = transforms.Compose(
    [
        transforms.ToTensor(),
        transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
    ]
)

im = Image.fromarray(im)
im = im_transform(im)
im = im[None, :, :, :]
im_b, im_c, im_h, im_w = im.shape
if max(im_h, im_w) < ref_size or min(im_h, im_w) > ref_size:
    if im_w >= im_h:
        im_h = ref_size
        im_w = int(im_w / im_h * ref_size)
    elif im_w < im_h:
        im_w = ref_size
        im_rh = int(im_h / im_w * ref_size)
    else:
        im_rh = im_h
        im_w = im_w
im_w = im_w - im_w % 32

im = F.interpolate(im, size=(im_h, im_w), mode='area')

_, _, matte = modnet(im.cuda() if torch.cuda.is_available() else im, True)

matte = F.interpolate(matte, size=(im_h, im_w), mode='area')
matte = matte[0][0].data.cpu().numpy()
matte_temp = './images/result.jpg'

matte_name = matte_temp.split('.')[0] + '.jpg'
print(matte_name)
#Image.fromarray(((matte * 255).astype('uint8')), mode='L').save(os.path.join('images/result', matte_name))
Image.fromarray(((matte * 255).astype('uint8')), mode='L').save(f'./images/kunresult.jpg')