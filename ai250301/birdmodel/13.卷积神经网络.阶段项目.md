# 卷积神经网络项目实现文档

关于项目实现的文档说明书，三个要素：数据、模型、训练

## 1、项目简介

关于项目的基本介绍。

- **大颗粒度分类**：以物种作为分类，比如飞机、青蛙、狗、猫、马、鹿等。

- **小颗粒度分类**：就是一个物种，比如麻雀，麻雀又分很多种类。比如牡丹花里面又有很多分类。

  | <img src="project/image-20241209134145018.png" alt="image-20241209134145018" style="zoom: 50%;" /> | <img src="project/image-20241209134259742.png" alt="image-20241209134259742" style="zoom:50%;" /> |
  | :----------------------------------------------------------: | :----------------------------------------------------------: |
  |                      小颗粒度分类之麻雀                      |                     小颗粒度分类之牡丹花                     |

  

- **实体颗粒度分类**：具体到具体的人，比如指纹识别、人脸识别等具体的个体，具体的实体

### 1.1 项目名称

​		基于CNN实现麻雀的小颗粒度分类

### 1.2 项目简介

​		该项目旨在通过卷积神经网络（CNN）实现麻雀的小颗粒度分类，主要针对麻雀的不同种类进行细粒度的视觉识别与分类。麻雀种类繁多，且外形差异较小，因此传统的图像分类方法难以满足精确识别的需求。通过引入深度学习中的CNN模型，本项目将构建一个专门用于麻雀细粒度分类的网络架构，充分利用卷积层的局部特征提取能力以及深层网络的高阶特征融合能力。项目将包括数据预处理、特征提取、模型训练与评估等步骤，使用麻雀图像数据集进行模型训练，并通过精细的特征学习提升分类精度。此外，项目还将探索数据增强、迁移学习等技术手段，以提高模型的泛化能力和鲁棒性，最终实现高精度的小颗粒度麻雀分类。



## 2、数据

公开数据源加上自己爬取的数据源

### 2.1 公开数据集

推荐：https://www.kaggle.com/datasets  里面去找

如果实在找不着就百度。

### 2.2 自己爬取

使用request模块

比如：https://image.baidu.com/search/index?tn=baiduimage&ipn=r&ct=201326592&cl=2&lm=&st=-1&fm=index&fr=&hs=0&xthttps=111110&sf=1&fmq=&pv=&ic=0&nc=1&z=&se=&showtab=0&fb=0&width=&height=&face=0&istype=2&ie=utf-8&word=%E4%B8%9B%E6%9E%97%E9%BA%BB%E9%9B%80

进行数据清洗和整理。

### 2.3 数据增强

提升模型的泛化能力和鲁棒性。

```python
transform = transforms.Compose(
        [
            transforms.RandomHorizontalFlip(),  # 随机水平翻转
            transforms.RandomRotation(10),  # 随机旋转 ±10 度
            transforms.RandomResizedCrop(
                32, scale=(0.8, 1.0)
            ),  # 随机裁剪到 32x32，缩放比例在0.8到1.0之间
            transforms.ColorJitter(
                brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1
            ),  # 随机调整亮度、对比度、饱和度、色调
            transforms.ToTensor(),  # 转换为 Tensor
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),  # 归一化
        ]
    )
```



### 2.4 数据分割



## 3. 神经网络

选择一个你觉得合适的经典神经网络，或者自己构建

<img src="project/image-20241209140041067.png" alt="image-20241209140041067" style="zoom: 80%;" />

## 4. 模型训练

和训练相关的操作

### 4.1 训练参数

轮次：ecpochs = 200

批次：batch_size=64

学习率：lr=1e-3



### 4.2 损失函数

交叉熵损失



### 4.3 优化器

使用动量优化器

optim.Adam()



### 4.4 训练过程可视化

使用tensorBoard  和 wandb

| ![image-20241209140830926](project/image-20241209140830926.png) | ![image-20241209140535075](project/image-20241209140535075.png) |
| :----------------------------------------------------------: | :----------------------------------------------------------: |
|                         准确率走势图                         |                         损失率走势图                         |
| <img src="project/image-20241209140925667.png" alt="image-20241209140925667" style="zoom: 50%;" /> | ![image-20241209140953247](project/image-20241209140953247.png) |
|                         数据增强效果                         |                         数据增强效果                         |

网络结构：

<img src="project/image-20241209141034780.png" alt="image-20241209141034780" style="zoom:50%;" />

## 5. 模型验证

验证我们的模型的鲁棒性和泛化能力

### 5.1 验证过程数据化

生成Excel：截个图看一下

<img src="project/image-20241209141153271.png" alt="image-20241209141153271" style="zoom:50%;" />



### 5.2 指标报表


准确度:8.96750088
精确度:0.96739231
召回率:0.96716710

<img src="project/image-20241209141314268.png" alt="image-20241209141314268" style="zoom:50%;" />

### 5.3 混淆矩阵

可视化

| <img src="project/image-20241209141433620.png" alt="image-20241209141433620" style="zoom:50%;" /> |
| :----------------------------------------------------------: |
|                        混淆矩阵可视化                        |



## 6. 模型优化

让网络变得更好

### 6.1 增加网络深度

让网络变得更好

```python
提供对应的代码
```



### 6.2 继续训练

让网络变得更好

```python
提供对应的代码
```



### 6.3 预训练和迁移学习

让网络变得更好

```python
import time
import torch
import torchvision

    ......

if __name__ == "__main__":
    pretrained()
```



## 7. 模型应用

推理工作

### 7.1 图片预处理

opencv的操作：二值化、轮廓、边缘填充等

```python
```

### 7.2 模型推理

```python
```

### 7.3 类别显示

```python
```



## 8. 模型移植

使用ONNX

### 8.1 导出ONNX

![image-20241209142641631](project/image-20241209142641631.png)



### 8.2 使用ONNX推理

```python
import cv2
import onnx
import onnxruntime as ort
from torchvision import transforms

transformdata = transforms.Compose(
    [
        transforms.ToTensor(),
        transforms.Resize((32, 32)),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2471, 0.2435, 0.2616)),
    ]
)


def imgread(img_path):
    imgdata = cv2.imread(img_path)
    # 转换成RGB
    imgdata = cv2.cvtColor(imgdata, cv2.COLOR_BGR2RGB)
    imgdata = transformdata(imgdata)
    # tensor ---> CHW  --->NCHW
    imgdata = imgdata.unsqueeze(0).numpy()
    return imgdata


def inference():
    # 加载onnx模型
    model = ort.InferenceSession(
        "./weights/resnet18.onnx", providers=["CPUExecutionProvider"]
    )
    imgdata = imgread("./images/171903065770_403_e8d90a3ec173f342c8e2b21356856a5faff14463.jpg")
    out = model.run(None, {"input": imgdata})
    classlabels = ["飞机", "汽车", "鸟", "猫", "鹿", "狗", "青蛙", "马", "船", "卡车"]
    print(classlabels[list(out[0][0]).index(max(out[0][0]))])


if __name__ == "__main__":
    inference()

```



## 9. 项目总结

自己要学会记录

### 9.1 问题及解决办法

在进行基于CNN实现麻雀的小颗粒度分类项目时，可能会遇到以下问题及解决办法：

**模板**

通过这些方法，可以逐步解决在项目开发过程中遇到的问题，优化模型性能，提升麻雀细粒度分类的准确性。

### 9.2 收获

记录一下





