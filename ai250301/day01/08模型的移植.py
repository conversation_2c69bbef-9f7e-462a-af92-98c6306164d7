import torch
import torch.nn as nn
from model import NumberModel
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

model = NumberModel()
model.to(device)
model.load_state_dict(torch.load('./model/last.pth'))
onnx_path = "./model/last.onnx"
# 创建一个实例输入
x = torch.randn(1, 1, 32, 32, device=device)
# 导出onnx
torch.onnx.export(
    model,
    x,
    onnx_path,
    #
    verbose=True, # 输出转换过程
    input_names=["input"],
    output_names=["output"],
)
print("onnx导出成功")