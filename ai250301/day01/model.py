import torch
import torch.nn as nn
class NumberModel(nn.Module):
    def __init__(self):
        super(NumberModel, self).__init__()
        self.c1 = nn.Sequential(
            nn.Conv2d(
                in_channels=1,
                out_channels=6,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )

        self.s2 = nn.AdaptiveAvgPool2d(14)
        self.c3 = nn.Sequential(
            nn.Conv2d(
                in_channels=6,
                out_channels=16,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )
        self.s4 = nn.AdaptiveAvgPool2d(5)
        self.l5 = nn.Sequential(
            nn.Linear(16*5*5,120),
            nn.ReLU(),
        )
        self.l6 = nn.Sequential(
            nn.Linear(120,84),
            nn.ReLU(),
        )
        self.l7 = nn.Linear(84,10)


    def forward(self, x):
        x = self.c1(x)
        x = self.s2(x)
        x = self.c3(x)
        x = self.s4(x)
        x = x.view(x.size(0), -1)
        x = self.l5(x)
        x = self.l6(x)
        out = self.l7(x)
        return out