import pandas as pd
import os

from sklearn.metrics import classification_report

csv_path = os.path.relpath(os.path.join(os.path.dirname(__file__), 'results', 'number.csv'))
# 读取CSV数据
csvdata = pd.read_csv(csv_path, index_col=0)
# 拿到真实标签
true_label = csvdata["label"].values
# 拿到预测标签
true_pred = csvdata["pred"].values

# 根据预测值和真实值生成分类报告
report = classification_report(y_true=true_label, y_pred=true_pred)
print(report)

