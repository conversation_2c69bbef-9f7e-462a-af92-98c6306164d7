import torch
import torch.nn as nn
import torchvision
from torch.utils.data import DataLoader
from torchvision.datasets import MNIST
from torchvision import transforms
import os
import pandas as pd
import numpy as np

# 数据集的地址
data_dir = os.path.relpath(os.path.join(os.path.dirname(__file__), 'data'))
# 模型保存的地址
model_dir = os.path.relpath(os.path.join(os.path.dirname(__file__), 'model', 'last.pth'))
tfs = transforms.Compose([
    transforms.ToTensor(),
    transforms.Resize(32),
    # # 图片的旋转
    # transforms.RandomRotation(35),
    # # 图片的裁剪
    # transforms.RandomCrop(28, padding=2),
    # # 图片的镜像
    # transforms.RandomHorizontalFlip(),
])

val_dataset = MNIST(
    root=data_dir,
    train=False,
    transform=tfs,
    download=False
)

classNames = val_dataset.classes


class NumberModel(nn.Module):
    def __init__(self):
        super(NumberModel, self).__init__()
        self.c1 = nn.Sequential(
            nn.Conv2d(
                in_channels=1,
                out_channels=6,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )

        self.s2 = nn.AdaptiveAvgPool2d(14)
        self.c3 = nn.Sequential(
            nn.Conv2d(
                in_channels=6,
                out_channels=16,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )
        self.s4 = nn.AdaptiveAvgPool2d(5)
        self.l5 = nn.Sequential(
            nn.Linear(16*5*5,120),
            nn.ReLU(),
        )
        self.l6 = nn.Sequential(
            nn.Linear(120,84),
            nn.ReLU(),
        )
        self.l7 = nn.Linear(84,10)


    def forward(self, x):
        x = self.c1(x)
        x = self.s2(x)
        x = self.c3(x)
        x = self.s4(x)
        x = x.view(x.size(0), -1)
        x = self.l5(x)
        x = self.l6(x)
        out = self.l7(x)
        return out

# 设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

model = NumberModel()
model.to(device)
# 加载预训练好的权重参数
model.load_state_dict(torch.load(model_dir))

model.eval()
acc_total = 0
val_dataloader = DataLoader(val_dataset, batch_size=64, shuffle=False)
total_data = np.empty((0,12))
with torch.no_grad():
    # 每个批次
    for x, y in val_dataloader:
        x = x.to(device)
        y = y.to(device)
        out = model(x)
        # [64,10]
        pred = torch.detach(out).cpu().numpy()
        # [64,]
        p1 = torch.argmax(out, dim=1)
        # 转化为numpy
        p2 = p1.unsqueeze(dim=1).detach().cpu().numpy()
        label = y.unsqueeze(dim=1).detach().cpu().numpy()
        batch_data = np.concatenate([pred, p2, label],axis=1)
        total_data = np.concatenate([total_data, batch_data], axis=0)


# 构建csv文件的第一行（列名）
pd_columns = [*classNames, 'pred', 'label']

csv_path = os.path.relpath(os.path.join(os.path.dirname(__file__), 'results', 'number.csv'))

pd.DataFrame(total_data, columns=pd_columns).to_csv(csv_path, index=False)