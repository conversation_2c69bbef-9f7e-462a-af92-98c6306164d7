from datetime import datetime
import torch
import torch.nn as nn
import torchvision
from torch.utils.data import DataLoader
from torchvision.datasets import MNIST
from torchvision import transforms
import torch.optim as opt
import os
from torch.utils.tensorboard import SummaryWriter


# 数据集的地址
data_dir = os.path.relpath(os.path.join(os.path.dirname(__file__), 'data'))
# 模型保存的地址
model_dir = os.path.relpath(os.path.join(os.path.dirname(__file__), 'model', 'last.pth'))
tfs = transforms.Compose([
    transforms.ToTensor(),
    transforms.Resize(32),
    # 图片的旋转
    transforms.RandomRotation(35),
    # 图片的裁剪
    transforms.RandomCrop(28, padding=2),
    # 图片的镜像
    transforms.RandomHorizontalFlip(),
])

train_dataset = MNIST(
    root=data_dir,
    train=True,
    transform=tfs,
    download=False
)
cls_index = train_dataset.class_to_idx
index_cls = {v: k for k, v in cls_index.items()}
cls_names = train_dataset.classes
print(index_cls)
print(cls_names)

# 网络
class NumberModel(nn.Module):
    def __init__(self):
        super(NumberModel, self).__init__()
        self.c1 = nn.Sequential(
            nn.Conv2d(
                in_channels=1,
                out_channels=6,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )

        self.s2 = nn.AdaptiveAvgPool2d(14)
        self.c3 = nn.Sequential(
            nn.Conv2d(
                in_channels=6,
                out_channels=16,
                kernel_size=5,
                stride=1,
            ),
            nn.ReLU(),
        )
        self.s4 = nn.AdaptiveAvgPool2d(5)
        self.l5 = nn.Sequential(
            nn.Linear(16*5*5,120),
            nn.ReLU(),
        )
        self.l6 = nn.Sequential(
            nn.Linear(120,84),
            nn.ReLU(),
        )
        self.l7 = nn.Linear(84,10)


    def forward(self, x):
        x = self.c1(x)
        x = self.s2(x)
        x = self.c3(x)
        x = self.s4(x)
        x = x.view(x.size(0), -1)
        x = self.l5(x)
        x = self.l6(x)
        out = self.l7(x)
        return out


# 设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

model = NumberModel()
# 加载模型
model.load_state_dict(torch.load(model_dir))
model.to(device)
# 训练轮次
epochs = 10
# 学习率
lr = 0.001
# 优化器
opt = opt.Adam(model.parameters(), lr=lr)
#损失函数
loss_fn = nn.CrossEntropyLoss(reduction="sum")

data_loader = DataLoader(dataset=train_dataset, batch_size=64, shuffle=True)


# dir = os.path.dirname(__file__)
# tbpath = os.path.join(dir, "tensorboard")
# 指定tensorboard日志保存路径
writer = SummaryWriter()
# 可视化网络结构
# writer.add_graph(model, input_to_model=torch.randn(64, 1, 32, 32))



# 训练
for epoch in range(epochs):
    acc_total = 0
    total_loss = 0
    for i, (x, y) in enumerate(data_loader):
        if i % 100 == 0:
            img_grid = torchvision.utils.make_grid(x)
            writer.add_image(f"r_m_{epoch}_{i * 100}", img_grid, epoch * len(train_dataset) + i)
        x = x.to(device)
        # y [64]   [1,2,5,7,.......]
        y = y.to(device)
        # [64,10]
        out = model(x)
        # out [64]  [1,3,2,5,.......]
        out1 = torch.argmax(out, dim=1)
        acc_total += (out1 == y).sum().item()
        loss = loss_fn(out, y)
        total_loss += loss.item()
        opt.zero_grad()
        loss.backward()
        opt.step()
    # 模型保存(需要保留两个，一个是训练完的轮次中效果最好的一个，一个是最后一轮训练完的结果)
    torch.save(model.state_dict(), model_dir)
    print("epoch:", epoch, "loss:", total_loss/ len(data_loader.dataset), "acc:", acc_total / len(data_loader.dataset))
    # 损失可视化
    writer.add_scalar("loss/train", total_loss/ len(data_loader.dataset), epoch)
    # 准确率可视化
    writer.add_scalar("acc/train", acc_total / len(data_loader.dataset), epoch)

writer.close()
