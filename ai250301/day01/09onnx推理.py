import onnxruntime as ort
import cv2
import numpy as np

img = cv2.imread('./images/7.jpg')
print(img.shape)
img_numpy = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
print(img_numpy.shape)
img_numpy = cv2.resize(img_numpy, (32, 32)).astype(np.float32)
img_numpy = np.expand_dims(img_numpy, axis=0)
img_numpy = np.expand_dims(img_numpy, axis=0)
print(img_numpy.shape)


# 设置 ONNX Runtime 使用 GPU
providers = ["CUDAExecutionProvider"]
# 加载onnx模型
sess = ort.InferenceSession('./model/last.onnx',providers=providers)

# 运行onnx模型
outputs = sess.run(None, {"input": img_numpy})
output = outputs[0]
print(np.argmax(output, axis=1))

