import torch
import torch.nn as nn
from torchvision.datasets import ImageFolder
from torchvision import transforms


train_data_set = ImageFolder(root='./train', transform=transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406],
                         std=[0.229, 0.224, 0.225])
]))

dc = train_data_set.class_to_idx
# print(dc)
idx_class = {v: k for k, v in dc.items()}
print(idx_class)

names = train_data_set.classes
print(names)