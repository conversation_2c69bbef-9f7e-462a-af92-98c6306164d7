import torch
import torch.nn as nn

def test01():
    inputData = torch.randn(1, 256, 224, 224)
    # 512*32*3*3
    conv = nn.Conv2d(
        in_channels=256,
        out_channels=512,
        kernel_size=3,
        stride=1,
        groups=8,
    )
    for name, param in conv.named_parameters():
        print(param.shape)

def test02():
    inputData = torch.randn(1, 256, 224, 224)
    # 512*256*3*3
    conv = nn.Conv2d(
        in_channels=256,
        out_channels=512,
        kernel_size=3,
        stride=1,
    )
    for name, param in conv.named_parameters():
        print(param.shape)

if __name__ == '__main__':
    test01()
    test02()