import torch
import torch.nn as nn

def test01():
    input_data = torch.randn(1, 3, 224, 224)
    # 64*3*3*3
    conv = nn.Conv2d(
        in_channels=3,
        out_channels=64,
        kernel_size=3,
        stride=1,
    )

    output = conv(input_data)
    print(output.shape)

def test02():
    input_data = torch.randn(1, 3, 224, 224)
    # 64*3*3*1
    conv1 = nn.Conv2d(
        in_channels=3,
        out_channels=64,
        kernel_size=(3, 1),
        stride=1,
    )
    # 64*3*1*3
    conv2 = nn.Conv2d(
        in_channels=64,
        out_channels=64,
        kernel_size=(1, 3),
        stride=1,
    )

    x = conv1(input_data)
    output = conv2(x)
    print(output.shape)

if __name__ == '__main__':
    test02()