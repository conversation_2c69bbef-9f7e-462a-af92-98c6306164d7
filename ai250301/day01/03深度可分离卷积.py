import torch
import torch.nn as nn


def test01():
    input_data = torch.randn(1, 8, 6, 7)
    # 8*8*3*3
    conv = nn.Conv2d(
        in_channels=8,
        out_channels=8,
        kernel_size=3,
        stride=1,
    )
    output = conv(input_data)
    # print(output.shape)
    for name, param in conv.named_parameters():
        print(name,param.shape)

def test02():
    input_data = torch.randn(1, 8, 6, 7)
    # 8*1*3*3
    conv = nn.Conv2d(
            in_channels=8,
            out_channels=8,
            kernel_size=3,
            stride=1,
            groups=8
        )
    output = conv(input_data)
    # for name, param in conv.named_parameters():
    #     print(name, param.shape)

    # 8*8*1*1
    conv2 = nn.Conv2d(
        in_channels=8,
        out_channels=8,
        kernel_size=1,
        stride=1,
    )
    print(conv2(output).shape)

if __name__ == '__main__':
    test01()
    test02()