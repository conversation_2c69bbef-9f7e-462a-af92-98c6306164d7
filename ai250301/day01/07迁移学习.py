from torchvision.models import resnet18
import torch
import torch.nn as nn
import torch.optim as opt


# 假设需要做一个10分类（这10个类别在IMGNET分类中；某几个类别在IMGNET分类中）
# model = resnet18(pretrained=True)
# # 保存模型
# torch.save(model.state_dict(), "./model/resnet18.pth")
# print(model)
model = resnet18()
# 得到全连接的输入特征数
in_features = model.fc.in_features
# 将输出改为10分类
model.fc = nn.Linear(in_features, 10)

pre_weights = torch.load("./model/resnet18.pth")

pre_weights.pop("fc.weight")
pre_weights.pop("fc.bias")

torch.save(model.state_dict(),'./model/rn18.pth')
no_pre_weight = torch.load('./model/rn18.pth')

no_pre_weight.update(pre_weights)
model.load_state_dict(no_pre_weight)

for name, param in model.named_parameters():
    if name != "fc.weight" and name != "fc.bias":
        param.requires_grad = False

# 开始筛选需要进行梯度更新的参数，而不是全部
params_grade_true = filter(lambda x: x.requires_grad, model.parameters() )


opt.Adam(params_grade_true, lr=0.01)

print(model)