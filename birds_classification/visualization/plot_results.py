# -*- coding: utf-8 -*-
"""
结果可视化模块
实现训练过程和结果的可视化
"""

import json
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional

from config import config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_training_history(history_path: str = None, save_path: str = None):
    """
    绘制训练历史曲线
    
    Args:
        history_path: 训练历史文件路径
        save_path: 保存路径
    """
    if history_path is None:
        history_path = config.RESULTS_DIR / 'training_history.json'
    
    # 加载训练历史
    with open(history_path, 'r', encoding='utf-8') as f:
        history = json.load(f)
    
    train_history = history['train']
    val_history = history['val']
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('训练过程可视化', fontsize=16, fontweight='bold')
    
    epochs = range(1, len(train_history['loss']) + 1)
    
    # 损失曲线
    axes[0, 0].plot(epochs, train_history['loss'], 'b-', label='训练损失', linewidth=2)
    axes[0, 0].plot(epochs, val_history['loss'], 'r-', label='验证损失', linewidth=2)
    axes[0, 0].set_title('损失曲线')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 准确率曲线
    axes[0, 1].plot(epochs, train_history['accuracy'], 'b-', label='训练准确率', linewidth=2)
    axes[0, 1].plot(epochs, val_history['accuracy'], 'r-', label='验证准确率', linewidth=2)
    axes[0, 1].set_title('准确率曲线')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 精确度曲线
    axes[1, 0].plot(epochs, train_history['precision'], 'b-', label='训练精确度', linewidth=2)
    axes[1, 0].plot(epochs, val_history['precision'], 'r-', label='验证精确度', linewidth=2)
    axes[1, 0].set_title('精确度曲线')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 召回率曲线
    axes[1, 1].plot(epochs, train_history['recall'], 'b-', label='训练召回率', linewidth=2)
    axes[1, 1].plot(epochs, val_history['recall'], 'r-', label='验证召回率', linewidth=2)
    axes[1, 1].set_title('召回率曲线')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Recall')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'training_curves.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"训练曲线保存到: {save_path}")
    
    plt.show()

def plot_confusion_matrix(confusion_matrix: np.ndarray, class_names: List[str] = None, 
                         save_path: str = None, normalize: bool = True):
    """
    绘制混淆矩阵
    
    Args:
        confusion_matrix: 混淆矩阵
        class_names: 类别名称
        save_path: 保存路径
        normalize: 是否归一化
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    # 归一化
    if normalize:
        cm = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis]
        title = '归一化混淆矩阵'
        fmt = '.2f'
    else:
        cm = confusion_matrix
        title = '混淆矩阵'
        fmt = 'd'
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    
    # 绘制热力图
    sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': '比例' if normalize else '数量'})
    
    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel('预测类别', fontsize=12)
    plt.ylabel('真实类别', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'confusion_matrix.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"混淆矩阵保存到: {save_path}")
    
    plt.show()

def plot_class_distribution(data_splits: Dict, save_path: str = None):
    """
    绘制类别分布图
    
    Args:
        data_splits: 数据分割字典
        save_path: 保存路径
    """
    # 统计每个类别的样本数量
    class_counts = {split: {class_name: 0 for class_name in config.CLASS_NAMES} 
                   for split in ['train', 'val', 'test']}
    
    for split_name, data_list in data_splits.items():
        for item in data_list:
            class_counts[split_name][item['label']] += 1
    
    # 创建图形
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    
    # 堆叠柱状图
    class_names = config.CLASS_NAMES
    train_counts = [class_counts['train'][name] for name in class_names]
    val_counts = [class_counts['val'][name] for name in class_names]
    test_counts = [class_counts['test'][name] for name in class_names]
    
    x = np.arange(len(class_names))
    width = 0.6
    
    axes[0].bar(x, train_counts, width, label='训练集', alpha=0.8)
    axes[0].bar(x, val_counts, width, bottom=train_counts, label='验证集', alpha=0.8)
    axes[0].bar(x, test_counts, width, 
               bottom=np.array(train_counts) + np.array(val_counts), 
               label='测试集', alpha=0.8)
    
    axes[0].set_title('各类别样本分布')
    axes[0].set_xlabel('鸟类种类')
    axes[0].set_ylabel('样本数量')
    axes[0].set_xticks(x)
    axes[0].set_xticklabels(class_names, rotation=45, ha='right')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 饼图显示总体分布
    total_counts = [train_counts[i] + val_counts[i] + test_counts[i] 
                   for i in range(len(class_names))]
    
    axes[1].pie(total_counts, labels=class_names, autopct='%1.1f%%', startangle=90)
    axes[1].set_title('总体类别分布')
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'class_distribution.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"类别分布图保存到: {save_path}")
    
    plt.show()

def plot_sample_images(data_loader, class_names: List[str] = None, 
                      num_samples: int = 16, save_path: str = None):
    """
    绘制样本图像
    
    Args:
        data_loader: 数据加载器
        class_names: 类别名称
        num_samples: 显示的样本数量
        save_path: 保存路径
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    # 获取一批数据
    data_iter = iter(data_loader)
    images, labels = next(data_iter)
    
    # 反标准化
    from utils.transforms import denormalize_tensor
    
    # 创建子图
    rows = int(np.sqrt(num_samples))
    cols = int(np.ceil(num_samples / rows))
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, 15))
    fig.suptitle('数据集样本展示', fontsize=16, fontweight='bold')
    
    for i in range(min(num_samples, len(images))):
        row = i // cols
        col = i % cols
        
        # 反标准化图像
        img = denormalize_tensor(images[i])
        img = torch.clamp(img, 0, 1)
        img = img.permute(1, 2, 0).numpy()
        
        if rows == 1:
            ax = axes[col] if cols > 1 else axes
        else:
            ax = axes[row, col]
        
        ax.imshow(img)
        ax.set_title(f'{class_names[labels[i]]}', fontsize=10)
        ax.axis('off')
    
    # 隐藏多余的子图
    for i in range(num_samples, rows * cols):
        row = i // cols
        col = i % cols
        if rows == 1:
            ax = axes[col] if cols > 1 else axes
        else:
            ax = axes[row, col]
        ax.axis('off')
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'sample_images.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"样本图像保存到: {save_path}")
    
    plt.show()

def plot_model_comparison(results_dict: Dict[str, Dict], save_path: str = None):
    """
    绘制模型比较图
    
    Args:
        results_dict: 模型结果字典
        save_path: 保存路径
    """
    models = list(results_dict.keys())
    metrics = ['accuracy', 'precision', 'recall', 'f1_score']
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('模型性能比较', fontsize=16, fontweight='bold')
    
    for i, metric in enumerate(metrics):
        row = i // 2
        col = i % 2
        
        values = [results_dict[model][metric] for model in models]
        
        bars = axes[row, col].bar(models, values, alpha=0.7)
        axes[row, col].set_title(f'{metric.capitalize()}')
        axes[row, col].set_ylabel(metric.capitalize())
        axes[row, col].set_ylim(0, 1)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            axes[row, col].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                               f'{value:.3f}', ha='center', va='bottom')
        
        axes[row, col].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'model_comparison.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"模型比较图保存到: {save_path}")
    
    plt.show()
