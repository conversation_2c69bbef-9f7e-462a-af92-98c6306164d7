# -*- coding: utf-8 -*-
"""
混淆矩阵可视化模块
专门用于混淆矩阵的创建和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
from typing import List, Optional
import pandas as pd

from config import config

def create_confusion_matrix_plot(y_true: List[int], y_pred: List[int], 
                                class_names: List[str] = None,
                                normalize: bool = True,
                                save_path: str = None,
                                figsize: tuple = (12, 10)):
    """
    创建混淆矩阵可视化
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        class_names: 类别名称
        normalize: 是否归一化
        save_path: 保存路径
        figsize: 图形大小
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    # 归一化处理
    if normalize:
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        cm_display = cm_normalized
        fmt = '.2f'
        title = '归一化混淆矩阵'
        cbar_label = '比例'
    else:
        cm_display = cm
        fmt = 'd'
        title = '混淆矩阵'
        cbar_label = '数量'
    
    # 创建图形
    plt.figure(figsize=figsize)
    
    # 使用seaborn绘制热力图
    sns.heatmap(cm_display, 
                annot=True, 
                fmt=fmt, 
                cmap='Blues',
                xticklabels=class_names, 
                yticklabels=class_names,
                cbar_kws={'label': cbar_label},
                square=True,
                linewidths=0.5)
    
    plt.title(title, fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('预测类别', fontsize=12)
    plt.ylabel('真实类别', fontsize=12)
    
    # 旋转标签以便更好显示
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'confusion_matrix_detailed.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"混淆矩阵保存到: {save_path}")
    
    plt.show()
    
    return cm

def plot_confusion_matrix_with_stats(y_true: List[int], y_pred: List[int],
                                   class_names: List[str] = None,
                                   save_path: str = None):
    """
    绘制带统计信息的混淆矩阵
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        class_names: 类别名称
        save_path: 保存路径
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    # 创建子图
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    # 左侧：原始混淆矩阵
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                ax=axes[0], square=True, linewidths=0.5)
    axes[0].set_title('混淆矩阵 (原始数量)', fontsize=14, fontweight='bold')
    axes[0].set_xlabel('预测类别')
    axes[0].set_ylabel('真实类别')
    
    # 右侧：归一化混淆矩阵
    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                ax=axes[1], square=True, linewidths=0.5)
    axes[1].set_title('混淆矩阵 (归一化)', fontsize=14, fontweight='bold')
    axes[1].set_xlabel('预测类别')
    axes[1].set_ylabel('真实类别')
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'confusion_matrix_comparison.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"混淆矩阵比较图保存到: {save_path}")
    
    plt.show()
    
    return cm

def analyze_confusion_matrix(cm: np.ndarray, class_names: List[str] = None):
    """
    分析混淆矩阵并生成报告
    
    Args:
        cm: 混淆矩阵
        class_names: 类别名称
    
    Returns:
        分析结果字典
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    n_classes = len(class_names)
    analysis = {}
    
    for i, class_name in enumerate(class_names):
        # 计算各项指标
        tp = cm[i, i]  # 真正例
        fp = cm[:, i].sum() - tp  # 假正例
        fn = cm[i, :].sum() - tp  # 假负例
        tn = cm.sum() - tp - fp - fn  # 真负例
        
        # 计算精确度、召回率、F1分数
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        # 计算支持度（该类别的真实样本数）
        support = cm[i, :].sum()
        
        analysis[class_name] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'support': support,
            'true_positive': tp,
            'false_positive': fp,
            'false_negative': fn,
            'true_negative': tn
        }
    
    return analysis

def plot_per_class_metrics(cm: np.ndarray, class_names: List[str] = None,
                          save_path: str = None):
    """
    绘制每个类别的性能指标
    
    Args:
        cm: 混淆矩阵
        class_names: 类别名称
        save_path: 保存路径
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    # 分析混淆矩阵
    analysis = analyze_confusion_matrix(cm, class_names)
    
    # 提取指标
    classes = list(analysis.keys())
    precision = [analysis[cls]['precision'] for cls in classes]
    recall = [analysis[cls]['recall'] for cls in classes]
    f1_score = [analysis[cls]['f1_score'] for cls in classes]
    
    # 创建图形
    x = np.arange(len(classes))
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(15, 8))
    
    bars1 = ax.bar(x - width, precision, width, label='精确度', alpha=0.8)
    bars2 = ax.bar(x, recall, width, label='召回率', alpha=0.8)
    bars3 = ax.bar(x + width, f1_score, width, label='F1分数', alpha=0.8)
    
    ax.set_xlabel('鸟类种类')
    ax.set_ylabel('分数')
    ax.set_title('各类别性能指标')
    ax.set_xticks(x)
    ax.set_xticklabels(classes, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 1.1)
    
    # 添加数值标签
    def add_value_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=8)
    
    add_value_labels(bars1)
    add_value_labels(bars2)
    add_value_labels(bars3)
    
    plt.tight_layout()
    
    # 保存图片
    if save_path is None:
        save_path = config.RESULTS_DIR / 'per_class_metrics.png'
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"各类别指标图保存到: {save_path}")
    
    plt.show()
    
    return analysis

def save_confusion_matrix_report(cm: np.ndarray, class_names: List[str] = None,
                               save_path: str = None):
    """
    保存混淆矩阵分析报告
    
    Args:
        cm: 混淆矩阵
        class_names: 类别名称
        save_path: 保存路径
    """
    if class_names is None:
        class_names = config.CLASS_NAMES
    
    if save_path is None:
        save_path = config.RESULTS_DIR / 'confusion_matrix_report.xlsx'
    
    # 分析混淆矩阵
    analysis = analyze_confusion_matrix(cm, class_names)
    
    with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
        # 保存原始混淆矩阵
        cm_df = pd.DataFrame(cm, index=class_names, columns=class_names)
        cm_df.to_excel(writer, sheet_name='原始混淆矩阵')
        
        # 保存归一化混淆矩阵
        cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        cm_norm_df = pd.DataFrame(cm_norm, index=class_names, columns=class_names)
        cm_norm_df.to_excel(writer, sheet_name='归一化混淆矩阵')
        
        # 保存各类别指标
        metrics_data = []
        for class_name, metrics in analysis.items():
            metrics_data.append({
                '类别': class_name,
                '精确度': metrics['precision'],
                '召回率': metrics['recall'],
                'F1分数': metrics['f1_score'],
                '支持度': metrics['support'],
                '真正例': metrics['true_positive'],
                '假正例': metrics['false_positive'],
                '假负例': metrics['false_negative'],
                '真负例': metrics['true_negative']
            })
        
        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.to_excel(writer, sheet_name='各类别指标', index=False)
    
    print(f"混淆矩阵报告保存到: {save_path}")
    
    return analysis
