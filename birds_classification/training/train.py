# -*- coding: utf-8 -*-
"""
训练模块
实现模型训练的核心逻辑
"""

import os
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

from config import config
from utils.metrics import MetricsTracker, print_metrics
from .validate import validate_model

class Trainer:
    """训练器类"""
    
    def __init__(self, model, train_loader, val_loader, device=None):
        """
        初始化训练器
        
        Args:
            model: 要训练的模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            device: 设备
        """
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        
        # 设备设置
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
        
        self.model.to(self.device)
        
        # 损失函数和优化器
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.LEARNING_RATE,
            weight_decay=config.WEIGHT_DECAY
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer, 
            step_size=50, 
            gamma=0.1
        )
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'accuracy': [],
            'precision': [],
            'recall': [],
            'f1_score': []
        }
        
        self.val_history = {
            'loss': [],
            'accuracy': [],
            'precision': [],
            'recall': [],
            'f1_score': []
        }
        
        # 最佳模型跟踪
        self.best_val_acc = 0.0
        self.best_epoch = 0
        
        # TensorBoard
        if config.USE_TENSORBOARD:
            log_dir = config.LOGS_DIR / f"experiment_{int(time.time())}"
            self.writer = SummaryWriter(log_dir)
            print(f"TensorBoard日志保存到: {log_dir}")
        else:
            self.writer = None
        
        # 创建保存目录
        config.create_directories()
    
    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """
        训练一个epoch
        
        Args:
            epoch: 当前epoch数
        
        Returns:
            训练指标字典
        """
        self.model.train()
        metrics_tracker = MetricsTracker()
        
        # 进度条
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{config.EPOCHS}')
        
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(self.device), target.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 更新指标
            metrics_tracker.update(output, target, loss.item())
            
            # 更新进度条
            if batch_idx % 10 == 0:
                current_metrics = metrics_tracker.compute_metrics()
                pbar.set_postfix({
                    'Loss': f"{current_metrics.get('loss', 0):.4f}",
                    'Acc': f"{current_metrics.get('accuracy', 0):.4f}"
                })
        
        # 计算epoch指标
        epoch_metrics = metrics_tracker.compute_metrics()
        
        return epoch_metrics
    
    def save_model(self, epoch: int, metrics: Dict[str, float], is_best: bool = False):
        """
        保存模型
        
        Args:
            epoch: 当前epoch
            metrics: 当前指标
            is_best: 是否为最佳模型
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'train_history': self.train_history,
            'val_history': self.val_history,
            'config': {
                'model_type': config.MODEL_TYPE,
                'num_classes': config.NUM_CLASSES,
                'class_names': config.CLASS_NAMES
            }
        }
        
        # 保存最新模型
        latest_path = config.WEIGHTS_DIR / 'latest_model.pth'
        torch.save(checkpoint, latest_path)
        
        # 保存最佳模型
        if is_best:
            best_path = config.WEIGHTS_DIR / 'best_model.pth'
            torch.save(checkpoint, best_path)
            print(f"保存最佳模型到: {best_path}")
        
        # 定期保存
        if (epoch + 1) % config.SAVE_FREQUENCY == 0:
            epoch_path = config.WEIGHTS_DIR / f'model_epoch_{epoch+1}.pth'
            torch.save(checkpoint, epoch_path)
    
    def load_checkpoint(self, checkpoint_path: str):
        """
        加载检查点
        
        Args:
            checkpoint_path: 检查点路径
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.train_history = checkpoint.get('train_history', self.train_history)
        self.val_history = checkpoint.get('val_history', self.val_history)
        
        start_epoch = checkpoint['epoch'] + 1
        print(f"从epoch {start_epoch} 继续训练")
        
        return start_epoch
    
    def train(self, start_epoch: int = 0):
        """
        完整训练流程
        
        Args:
            start_epoch: 开始的epoch数
        """
        print(f"开始训练，设备: {self.device}")
        print(f"训练集大小: {len(self.train_loader.dataset)}")
        print(f"验证集大小: {len(self.val_loader.dataset)}")
        
        for epoch in range(start_epoch, config.EPOCHS):
            epoch_start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(epoch)
            
            # 验证
            val_metrics = validate_model(
                self.model, self.val_loader, self.criterion, self.device
            )
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录历史
            for key in self.train_history.keys():
                if key in train_metrics:
                    self.train_history[key].append(train_metrics[key])
                if key in val_metrics:
                    self.val_history[key].append(val_metrics[key])
            
            # TensorBoard记录
            if self.writer:
                for key, value in train_metrics.items():
                    if isinstance(value, (int, float)):
                        self.writer.add_scalar(f'Train/{key}', value, epoch)
                
                for key, value in val_metrics.items():
                    if isinstance(value, (int, float)):
                        self.writer.add_scalar(f'Val/{key}', value, epoch)
                
                # 记录学习率
                current_lr = self.optimizer.param_groups[0]['lr']
                self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            # 检查是否为最佳模型
            is_best = val_metrics['accuracy'] > self.best_val_acc
            if is_best:
                self.best_val_acc = val_metrics['accuracy']
                self.best_epoch = epoch
            
            # 保存模型
            self.save_model(epoch, val_metrics, is_best)
            
            # 打印结果
            epoch_time = time.time() - epoch_start_time
            print(f"\nEpoch {epoch+1}/{config.EPOCHS} - {epoch_time:.2f}s")
            print(f"训练 - Loss: {train_metrics['loss']:.4f}, "
                  f"Acc: {train_metrics['accuracy']:.4f}")
            print(f"验证 - Loss: {val_metrics['loss']:.4f}, "
                  f"Acc: {val_metrics['accuracy']:.4f}")
            print(f"最佳验证准确率: {self.best_val_acc:.4f} (Epoch {self.best_epoch+1})")
            print("-" * 60)
        
        print(f"\n训练完成！最佳验证准确率: {self.best_val_acc:.4f}")
        
        # 关闭TensorBoard
        if self.writer:
            self.writer.close()
        
        # 保存训练历史
        self.save_training_history()
    
    def save_training_history(self):
        """保存训练历史"""
        history = {
            'train': self.train_history,
            'val': self.val_history,
            'best_val_acc': self.best_val_acc,
            'best_epoch': self.best_epoch
        }
        
        history_path = config.RESULTS_DIR / 'training_history.json'
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
        
        print(f"训练历史保存到: {history_path}")

def create_trainer(model, train_loader, val_loader, device=None):
    """
    创建训练器
    
    Args:
        model: 模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 设备
    
    Returns:
        训练器实例
    """
    return Trainer(model, train_loader, val_loader, device)
