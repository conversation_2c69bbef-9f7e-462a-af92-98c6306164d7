# -*- coding: utf-8 -*-
"""
验证模块
实现模型验证和测试功能
"""

import torch
import torch.nn as nn
from tqdm import tqdm
from typing import Dict, Tuple
import numpy as np

from utils.metrics import MetricsTracker, calculate_metrics

def validate_model(model, data_loader, criterion, device, verbose=False):
    """
    验证模型
    
    Args:
        model: 要验证的模型
        data_loader: 数据加载器
        criterion: 损失函数
        device: 设备
        verbose: 是否显示详细信息
    
    Returns:
        验证指标字典
    """
    model.eval()
    metrics_tracker = MetricsTracker()
    
    with torch.no_grad():
        if verbose:
            pbar = tqdm(data_loader, desc='Validating')
        else:
            pbar = data_loader
        
        for data, target in pbar:
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            output = model(data)
            loss = criterion(output, target)
            
            # 更新指标
            metrics_tracker.update(output, target, loss.item())
            
            if verbose and hasattr(pbar, 'set_postfix'):
                current_metrics = metrics_tracker.compute_metrics()
                pbar.set_postfix({
                    'Loss': f"{current_metrics.get('loss', 0):.4f}",
                    'Acc': f"{current_metrics.get('accuracy', 0):.4f}"
                })
    
    return metrics_tracker.compute_metrics()

def test_model(model, test_loader, device, class_names=None):
    """
    测试模型并生成详细报告
    
    Args:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 设备
        class_names: 类别名称列表
    
    Returns:
        测试结果字典
    """
    model.eval()
    metrics_tracker = MetricsTracker()
    
    print("开始模型测试...")
    
    with torch.no_grad():
        pbar = tqdm(test_loader, desc='Testing')
        
        for data, target in pbar:
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            output = model(data)
            
            # 更新指标（测试时不计算损失）
            metrics_tracker.update(output, target, 0.0)
            
            current_metrics = metrics_tracker.compute_metrics()
            pbar.set_postfix({
                'Acc': f"{current_metrics.get('accuracy', 0):.4f}"
            })
    
    # 计算最终指标
    final_metrics = metrics_tracker.compute_metrics()
    confusion_mat = metrics_tracker.get_confusion_matrix()
    classification_rep = metrics_tracker.get_classification_report()
    
    # 打印结果
    print("\n" + "="*60)
    print("测试结果")
    print("="*60)
    print(f"测试样本数量: {final_metrics['num_samples']}")
    print(f"准确率: {final_metrics['accuracy']:.4f}")
    print(f"精确度: {final_metrics['precision']:.4f}")
    print(f"召回率: {final_metrics['recall']:.4f}")
    print(f"F1分数: {final_metrics['f1_score']:.4f}")
    print("="*60)
    
    print("\n分类报告:")
    print(classification_rep)
    
    return {
        'metrics': final_metrics,
        'confusion_matrix': confusion_mat,
        'classification_report': classification_rep,
        'predictions': metrics_tracker.all_predictions,
        'targets': metrics_tracker.all_targets
    }

def predict_single_image(model, image_tensor, device, class_names=None):
    """
    对单张图像进行预测
    
    Args:
        model: 训练好的模型
        image_tensor: 图像张量 (1, C, H, W)
        device: 设备
        class_names: 类别名称列表
    
    Returns:
        预测结果字典
    """
    model.eval()
    
    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        output = model(image_tensor)
        
        # 获取概率
        probabilities = torch.softmax(output, dim=1)
        
        # 获取预测类别
        predicted_class = torch.argmax(output, dim=1).item()
        confidence = probabilities[0][predicted_class].item()
        
        # 获取top-k预测
        top_k = min(5, output.size(1))
        top_probs, top_indices = torch.topk(probabilities[0], top_k)
        
        top_predictions = []
        for i in range(top_k):
            class_idx = top_indices[i].item()
            prob = top_probs[i].item()
            class_name = class_names[class_idx] if class_names else str(class_idx)
            top_predictions.append({
                'class_name': class_name,
                'class_index': class_idx,
                'probability': prob
            })
    
    result = {
        'predicted_class': predicted_class,
        'predicted_class_name': class_names[predicted_class] if class_names else str(predicted_class),
        'confidence': confidence,
        'top_predictions': top_predictions
    }
    
    return result

def batch_predict(model, data_loader, device, class_names=None):
    """
    批量预测
    
    Args:
        model: 训练好的模型
        data_loader: 数据加载器
        device: 设备
        class_names: 类别名称列表
    
    Returns:
        预测结果列表
    """
    model.eval()
    all_predictions = []
    all_probabilities = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc='Predicting')
        
        for data, _ in pbar:
            data = data.to(device)
            output = model(data)
            
            # 获取概率和预测
            probabilities = torch.softmax(output, dim=1)
            predictions = torch.argmax(output, dim=1)
            
            all_predictions.extend(predictions.cpu().numpy().tolist())
            all_probabilities.extend(probabilities.cpu().numpy().tolist())
    
    # 转换为更友好的格式
    results = []
    for i, (pred, probs) in enumerate(zip(all_predictions, all_probabilities)):
        class_name = class_names[pred] if class_names else str(pred)
        confidence = probs[pred]
        
        results.append({
            'index': i,
            'predicted_class': pred,
            'predicted_class_name': class_name,
            'confidence': confidence,
            'all_probabilities': probs
        })
    
    return results

def evaluate_model_performance(model, test_loader, device, save_path=None):
    """
    全面评估模型性能
    
    Args:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 设备
        save_path: 结果保存路径
    
    Returns:
        评估结果字典
    """
    from config import config
    from utils.metrics import save_metrics_to_excel
    
    # 进行测试
    test_results = test_model(model, test_loader, device, config.CLASS_NAMES)
    
    # 保存结果
    if save_path:
        save_metrics_to_excel(
            test_results['metrics'],
            test_results['confusion_matrix'],
            save_path
        )
    
    return test_results
