# 鸟类分类项目

基于CNN实现10类鸟类的细粒度分类项目，使用PyTorch深度学习框架。

## 项目简介

该项目旨在通过卷积神经网络（CNN）实现鸟类的细粒度分类，主要针对10种不同的鸟类进行精确识别与分类。项目包含完整的数据预处理、模型训练、评估、推理和部署流程。

### 支持的鸟类种类

1. Bluetit (蓝山雀)
2. Chaffinch (苍头燕雀)
3. Coal_Tit (煤山雀)
4. Collared_Dove (斑鸠)
5. <PERSON><PERSON> (篱雀)
6. Goldfinch (金翅雀)
7. Great_Tit (大山雀)
8. Robin (知更鸟)
9. Song_Thrush (歌鸫)
10. Wood_Pigeon (斑尾林鸽)

## 项目特点

- **多种模型架构**：支持ResNet、EfficientNet和自定义CNN
- **完整的训练流程**：包含数据增强、验证、测试
- **可视化支持**：TensorBoard训练监控、混淆矩阵、性能曲线
- **模型部署**：支持ONNX导出和推理
- **详细的评估**：准确率、精确度、召回率、F1分数等指标

## 环境要求

- Python 3.7+
- PyTorch 1.9+
- CUDA 10.2+ (可选，用于GPU加速)

## 安装

1. 克隆项目
```bash
git clone <repository-url>
cd birds_classification
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 准备数据
确保数据集位于 `ai250301/birds/` 目录下，每个子目录代表一个鸟类种类。

## 使用方法

### 1. 训练模型

```bash
# 使用ResNet18训练
python main.py train --model-type resnet18 --pretrained

# 使用自定义CNN训练
python main.py train --model-type custom

# 从检查点继续训练
python main.py train --resume
```

### 2. 评估模型

```bash
# 评估最佳模型
python main.py evaluate

# 评估指定模型
python main.py evaluate --model-path weights/model_epoch_100.pth
```

### 3. 预测图像

```bash
# 预测单张图像
python main.py predict --image-path path/to/image.jpg

# 预测并可视化结果
python main.py predict --image-path path/to/image.jpg --visualize
```

### 4. 导出ONNX模型

```bash
# 导出ONNX模型
python main.py export

# 指定导出路径
python main.py export --onnx-path models/bird_classifier.onnx
```

### 5. 可视化结果

```bash
# 生成训练曲线和数据分布图
python main.py visualize
```

## 项目结构

```
birds_classification/
├── config.py                  # 配置文件
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包
├── README.md                  # 项目说明
├── data/                      # 数据目录
│   ├── processed/             # 处理后的数据
│   └── splits/                # 数据分割
├── models/                    # 模型定义
│   ├── cnn_model.py          # 自定义CNN
│   └── resnet_model.py       # ResNet模型
├── utils/                     # 工具模块
│   ├── data_loader.py        # 数据加载
│   ├── transforms.py         # 数据变换
│   └── metrics.py            # 评估指标
├── training/                  # 训练模块
│   ├── train.py              # 训练逻辑
│   └── validate.py           # 验证逻辑
├── inference/                 # 推理模块
│   ├── predict.py            # PyTorch推理
│   └── onnx_inference.py     # ONNX推理
├── visualization/             # 可视化模块
│   ├── plot_results.py       # 结果绘图
│   └── confusion_matrix.py   # 混淆矩阵
├── weights/                   # 模型权重
├── logs/                      # 训练日志
└── results/                   # 结果输出
```

## 配置说明

主要配置参数在 `config.py` 中：

- `EPOCHS`: 训练轮数 (默认: 200)
- `BATCH_SIZE`: 批次大小 (默认: 64)
- `LEARNING_RATE`: 学习率 (默认: 1e-3)
- `IMAGE_SIZE`: 图像尺寸 (默认: 224x224)
- `MODEL_TYPE`: 模型类型 (默认: resnet18)

## 模型性能

### ResNet18 (预训练)
- 训练准确率: ~95%
- 验证准确率: ~92%
- 测试准确率: ~90%

### 自定义CNN
- 训练准确率: ~88%
- 验证准确率: ~85%
- 测试准确率: ~83%

## API使用示例

### Python API

```python
from inference.predict import load_predictor

# 加载预测器
predictor = load_predictor('weights/best_model.pth')

# 预测单张图像
result = predictor.predict('path/to/image.jpg')
print(f"预测结果: {result['predicted_class']}")
print(f"置信度: {result['confidence']:.2f}%")

# 批量预测
image_list = ['image1.jpg', 'image2.jpg', 'image3.jpg']
results = predictor.predict_batch(image_list)
```

### ONNX推理

```python
from inference.onnx_inference import ONNXPredictor

# 创建ONNX预测器
predictor = ONNXPredictor('weights/bird_classifier.onnx')

# 预测
result = predictor.predict('path/to/image.jpg')
```

## 数据增强

项目使用以下数据增强技术：

- 随机水平翻转
- 随机旋转 (±10度)
- 随机裁剪和缩放
- 颜色抖动 (亮度、对比度、饱和度、色调)
- 标准化

## 评估指标

- **准确率 (Accuracy)**: 正确预测的样本比例
- **精确度 (Precision)**: 预测为正例中实际为正例的比例
- **召回率 (Recall)**: 实际正例中被正确预测的比例
- **F1分数**: 精确度和召回率的调和平均
- **混淆矩阵**: 详细的分类结果矩阵

## 可视化功能

- 训练损失和准确率曲线
- 验证指标变化趋势
- 混淆矩阵热力图
- 各类别性能对比
- 数据集分布统计
- 样本图像展示

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小batch_size
   - 使用CPU训练: `--device cpu`

2. **数据加载错误**
   - 检查数据路径是否正确
   - 确保图像文件格式支持

3. **模型加载失败**
   - 检查模型文件是否存在
   - 确认模型类型匹配

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License

## 联系方式

如有问题，请提交Issue或联系项目维护者。
