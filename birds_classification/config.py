# -*- coding: utf-8 -*-
"""
鸟类分类项目配置文件
基于CNN实现10类鸟类的细粒度分类
"""

import os
from pathlib import Path

class Config:
    """项目配置类"""
    
    # 项目基础路径
    PROJECT_ROOT = Path(__file__).parent
    DATA_ROOT = PROJECT_ROOT / "data"
    RAW_DATA_PATH = Path("ai250301/birds")  # 原始数据路径
    PROCESSED_DATA_PATH = DATA_ROOT / "processed"
    SPLITS_DATA_PATH = DATA_ROOT / "splits"
    
    # 模型相关路径
    MODELS_DIR = PROJECT_ROOT / "models"
    WEIGHTS_DIR = PROJECT_ROOT / "weights"
    LOGS_DIR = PROJECT_ROOT / "logs"
    RESULTS_DIR = PROJECT_ROOT / "results"
    
    # 数据集配置
    CLASS_NAMES = [
        'Bluetit', 'Chaffinch', 'Coal_Tit', 'Collared_Dove', 'Dunnock',
        'Goldfinch', 'Great_Tit', 'Robin', 'Song_Thrush', 'Wood_Pigeon'
    ]
    NUM_CLASSES = len(CLASS_NAMES)
    
    # 数据分割比例
    TRAIN_RATIO = 0.7
    VAL_RATIO = 0.15
    TEST_RATIO = 0.15
    
    # 图像处理参数
    IMAGE_SIZE = (224, 224)  # ResNet标准输入尺寸
    MEAN = [0.485, 0.456, 0.406]  # ImageNet预训练模型的均值
    STD = [0.229, 0.224, 0.225]   # ImageNet预训练模型的标准差
    
    # 训练参数
    BATCH_SIZE = 64
    EPOCHS = 200
    LEARNING_RATE = 1e-3
    WEIGHT_DECAY = 1e-4
    
    # 数据增强参数
    ROTATION_DEGREES = 10
    BRIGHTNESS = 0.2
    CONTRAST = 0.2
    SATURATION = 0.2
    HUE = 0.1
    SCALE_RANGE = (0.8, 1.0)
    
    # 模型配置
    MODEL_TYPE = "resnet18"  # 可选: "custom_cnn", "resnet18"
    PRETRAINED = True
    
    # 训练配置
    DEVICE = "cuda"  # 自动检测GPU
    NUM_WORKERS = 4
    PIN_MEMORY = True
    
    # 保存配置
    SAVE_BEST_ONLY = True
    SAVE_FREQUENCY = 10  # 每10个epoch保存一次
    
    # 可视化配置
    USE_TENSORBOARD = True
    USE_WANDB = False  # 需要wandb账号
    
    # ONNX导出配置
    ONNX_MODEL_PATH = WEIGHTS_DIR / "bird_classifier.onnx"
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.DATA_ROOT,
            cls.PROCESSED_DATA_PATH,
            cls.SPLITS_DATA_PATH,
            cls.WEIGHTS_DIR,
            cls.LOGS_DIR,
            cls.RESULTS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
    @classmethod
    def get_class_to_idx(cls):
        """获取类别到索引的映射"""
        return {class_name: idx for idx, class_name in enumerate(cls.CLASS_NAMES)}
    
    @classmethod
    def get_idx_to_class(cls):
        """获取索引到类别的映射"""
        return {idx: class_name for idx, class_name in enumerate(cls.CLASS_NAMES)}

# 创建配置实例
config = Config()
