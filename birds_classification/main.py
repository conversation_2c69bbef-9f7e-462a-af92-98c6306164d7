# -*- coding: utf-8 -*-
"""
鸟类分类项目主程序
基于CNN实现10类鸟类的细粒度分类
"""

import argparse
import sys
import torch
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import config
from utils.data_loader import create_data_loaders
from models.resnet_model import create_model
from models.cnn_model import create_custom_cnn
from training.train import create_trainer
from training.validate import evaluate_model_performance
from inference.predict import load_predictor
from inference.onnx_inference import export_to_onnx, ONNXPredictor
from visualization.plot_results import plot_training_history, plot_class_distribution
from visualization.confusion_matrix import create_confusion_matrix_plot

def train_model(args):
    """训练模型"""
    print("="*60)
    print("开始训练模型")
    print("="*60)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_data_loaders(args.batch_size)
    
    # 创建模型
    if args.model_type in ['resnet18', 'resnet34', 'resnet50', 'efficientnet_b0']:
        model = create_model(args.model_type, config.NUM_CLASSES, args.pretrained)
    else:
        model = create_custom_cnn(args.model_type, config.NUM_CLASSES)
    
    print(f"使用模型: {args.model_type}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    device = torch.device('cuda' if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    trainer = create_trainer(model, train_loader, val_loader, device)
    
    # 开始训练
    start_epoch = 0
    if args.resume:
        checkpoint_path = config.WEIGHTS_DIR / 'latest_model.pth'
        if checkpoint_path.exists():
            start_epoch = trainer.load_checkpoint(checkpoint_path)
        else:
            print("未找到检查点文件，从头开始训练")
    
    trainer.train(start_epoch)
    
    print("训练完成！")

def evaluate_model(args):
    """评估模型"""
    print("="*60)
    print("开始评估模型")
    print("="*60)
    
    # 创建数据加载器
    _, _, test_loader = create_data_loaders(args.batch_size)
    
    # 加载模型
    model_path = args.model_path or config.WEIGHTS_DIR / 'best_model.pth'
    if not Path(model_path).exists():
        print(f"模型文件不存在: {model_path}")
        return
    
    # 创建模型
    checkpoint = torch.load(model_path, map_location='cpu')
    model_config = checkpoint.get('config', {})
    model_type = model_config.get('model_type', config.MODEL_TYPE)
    
    if 'resnet' in model_type or 'efficientnet' in model_type:
        model = create_model(model_type, config.NUM_CLASSES, pretrained=False)
    else:
        model = create_custom_cnn('custom', config.NUM_CLASSES)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    model.to(device)
    
    # 评估
    save_path = config.RESULTS_DIR / 'test_results.xlsx'
    results = evaluate_model_performance(model, test_loader, device, save_path)
    
    # 生成混淆矩阵
    create_confusion_matrix_plot(
        results['targets'], 
        results['predictions'],
        config.CLASS_NAMES,
        save_path=config.RESULTS_DIR / 'confusion_matrix.png'
    )
    
    print("评估完成！")

def predict_image(args):
    """预测单张图像"""
    print("="*60)
    print("开始图像预测")
    print("="*60)
    
    if not Path(args.image_path).exists():
        print(f"图像文件不存在: {args.image_path}")
        return
    
    # 加载预测器
    predictor = load_predictor(args.model_path, device=args.device)
    
    # 预测
    result = predictor.predict(args.image_path, top_k=5)
    
    # 打印结果
    print(f"预测结果: {result['predicted_class']}")
    print(f"置信度: {result['confidence']:.2f}%")
    print("\nTop-5 预测:")
    for i, pred in enumerate(result['top_predictions'], 1):
        print(f"{i}. {pred['class_name']}: {pred['confidence']:.2f}%")
    
    # 可视化
    if args.visualize:
        save_path = config.RESULTS_DIR / f"prediction_{Path(args.image_path).stem}.png"
        predictor.visualize_prediction(args.image_path, save_path)

def export_onnx(args):
    """导出ONNX模型"""
    print("="*60)
    print("导出ONNX模型")
    print("="*60)
    
    model_path = args.model_path or config.WEIGHTS_DIR / 'best_model.pth'
    if not Path(model_path).exists():
        print(f"模型文件不存在: {model_path}")
        return
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location='cpu')
    model_config = checkpoint.get('config', {})
    model_type = model_config.get('model_type', config.MODEL_TYPE)
    
    if 'resnet' in model_type or 'efficientnet' in model_type:
        model = create_model(model_type, config.NUM_CLASSES, pretrained=False)
    else:
        model = create_custom_cnn('custom', config.NUM_CLASSES)
    
    # 导出ONNX
    onnx_path = args.onnx_path or config.ONNX_MODEL_PATH
    export_to_onnx(model, model_path, onnx_path)
    
    print(f"ONNX模型已保存到: {onnx_path}")

def visualize_results(args):
    """可视化结果"""
    print("="*60)
    print("生成可视化结果")
    print("="*60)
    
    # 绘制训练历史
    history_path = config.RESULTS_DIR / 'training_history.json'
    if history_path.exists():
        plot_training_history(history_path)
    else:
        print("未找到训练历史文件")
    
    # 绘制数据分布
    from utils.data_loader import load_data_splits
    data_splits = load_data_splits()
    if data_splits:
        splits_dict = {
            'train': data_splits[0],
            'val': data_splits[1], 
            'test': data_splits[2]
        }
        plot_class_distribution(splits_dict)
    else:
        print("未找到数据分割文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='鸟类分类项目')
    parser.add_argument('mode', choices=['train', 'evaluate', 'predict', 'export', 'visualize'],
                       help='运行模式')
    
    # 通用参数
    parser.add_argument('--model-type', default='resnet18',
                       choices=['resnet18', 'resnet34', 'resnet50', 'efficientnet_b0', 'custom', 'simple'],
                       help='模型类型')
    parser.add_argument('--model-path', type=str, help='模型路径')
    parser.add_argument('--device', default='cuda', choices=['cuda', 'cpu'], help='设备')
    parser.add_argument('--batch-size', type=int, default=config.BATCH_SIZE, help='批次大小')
    
    # 训练参数
    parser.add_argument('--pretrained', action='store_true', default=True, help='使用预训练模型')
    parser.add_argument('--resume', action='store_true', help='从检查点继续训练')
    
    # 预测参数
    parser.add_argument('--image-path', type=str, help='图像路径')
    parser.add_argument('--visualize', action='store_true', help='可视化预测结果')
    
    # ONNX参数
    parser.add_argument('--onnx-path', type=str, help='ONNX模型保存路径')
    
    args = parser.parse_args()
    
    # 创建必要的目录
    config.create_directories()
    
    # 根据模式执行相应功能
    if args.mode == 'train':
        train_model(args)
    elif args.mode == 'evaluate':
        evaluate_model(args)
    elif args.mode == 'predict':
        if not args.image_path:
            print("预测模式需要指定 --image-path 参数")
            return
        predict_image(args)
    elif args.mode == 'export':
        export_onnx(args)
    elif args.mode == 'visualize':
        visualize_results(args)

if __name__ == '__main__':
    main()
