# -*- coding: utf-8 -*-
"""
项目功能测试脚本
验证各个模块的基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_config():
    """测试配置模块"""
    print("="*50)
    print("测试配置模块")
    print("="*50)
    
    from config import config
    
    print(f"项目根目录: {config.PROJECT_ROOT}")
    print(f"类别数量: {config.NUM_CLASSES}")
    print(f"类别名称: {config.CLASS_NAMES}")
    print(f"图像尺寸: {config.IMAGE_SIZE}")
    print(f"批次大小: {config.BATCH_SIZE}")
    
    # 创建目录
    config.create_directories()
    print("目录创建成功")
    
    print("✅ 配置模块测试通过\n")

def test_transforms():
    """测试数据变换模块"""
    print("="*50)
    print("测试数据变换模块")
    print("="*50)

    try:
        from utils.transforms import get_train_transforms, get_val_transforms
        print("✅ 数据变换模块导入成功")
        print("⚠️  完整测试需要安装PyTorch和PIL")
    except ImportError as e:
        print(f"⚠️  数据变换模块导入失败: {e}")

    print("✅ 数据变换模块测试完成\n")

def test_models():
    """测试模型模块"""
    print("="*50)
    print("测试模型模块")
    print("="*50)

    try:
        from models.cnn_model import CustomCNN, count_parameters
        from models.resnet_model import BirdResNet
        print("✅ 模型模块导入成功")
        print("⚠️  完整测试需要安装PyTorch")
    except ImportError as e:
        print(f"⚠️  模型模块导入失败: {e}")

    print("✅ 模型模块测试完成\n")

def test_metrics():
    """测试评估指标模块"""
    print("="*50)
    print("测试评估指标模块")
    print("="*50)

    try:
        from utils.metrics import MetricsTracker, calculate_metrics
        print("✅ 评估指标模块导入成功")
        print("⚠️  完整测试需要安装scikit-learn和PyTorch")
    except ImportError as e:
        print(f"⚠️  评估指标模块导入失败: {e}")

    print("✅ 评估指标模块测试完成\n")

def test_data_loader():
    """测试数据加载模块"""
    print("="*50)
    print("测试数据加载模块")
    print("="*50)

    try:
        from utils.data_loader import BirdDataset
        print("✅ 数据加载模块导入成功")
        print("⚠️  完整测试需要真实图像文件和PyTorch")
    except ImportError as e:
        print(f"⚠️  数据加载模块导入失败: {e}")

    print("✅ 数据加载模块测试完成\n")

def test_project_structure():
    """测试项目结构"""
    print("="*50)
    print("测试项目结构")
    print("="*50)
    
    project_root = Path(__file__).parent
    
    # 检查主要文件
    required_files = [
        'config.py',
        'main.py',
        'requirements.txt',
        'README.md'
    ]
    
    # 检查主要目录
    required_dirs = [
        'models',
        'utils', 
        'training',
        'inference',
        'visualization'
    ]
    
    print("检查文件:")
    for file in required_files:
        file_path = project_root / file
        status = "✅" if file_path.exists() else "❌"
        print(f"  {status} {file}")
    
    print("\n检查目录:")
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        status = "✅" if dir_path.exists() else "❌"
        print(f"  {status} {dir_name}/")
    
    print("\n✅ 项目结构检查完成\n")

def main():
    """主测试函数"""
    print("🚀 开始项目功能测试")
    print("="*60)
    
    try:
        test_config()
        test_transforms()
        test_models()
        test_metrics()
        test_data_loader()
        test_project_structure()
        
        print("="*60)
        print("🎉 所有测试完成！项目基本功能正常")
        print("="*60)
        
        print("\n📋 下一步操作建议:")
        print("1. 检查数据集路径是否正确")
        print("2. 安装所有依赖包: pip install -r requirements.txt")
        print("3. 开始训练: python main.py train --model-type resnet18")
        print("4. 查看帮助: python main.py --help")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
