# -*- coding: utf-8 -*-
"""
ResNet模型
基于torchvision的ResNet实现鸟类分类
"""

import torch
import torch.nn as nn
import torchvision.models as models
from config import config

class BirdResNet(nn.Module):
    """
    基于ResNet的鸟类分类模型
    支持ResNet18, ResNet34, ResNet50等
    """
    
    def __init__(self, model_name='resnet18', num_classes=None, pretrained=True):
        super(BirdResNet, self).__init__()
        
        if num_classes is None:
            num_classes = config.NUM_CLASSES
        
        self.model_name = model_name
        self.num_classes = num_classes
        
        # 加载预训练模型
        if model_name == 'resnet18':
            self.backbone = models.resnet18(pretrained=pretrained)
        elif model_name == 'resnet34':
            self.backbone = models.resnet34(pretrained=pretrained)
        elif model_name == 'resnet50':
            self.backbone = models.resnet50(pretrained=pretrained)
        elif model_name == 'resnet101':
            self.backbone = models.resnet101(pretrained=pretrained)
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 获取原始全连接层的输入特征数
        in_features = self.backbone.fc.in_features
        
        # 替换最后的全连接层
        self.backbone.fc = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(in_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
        
        # 如果使用预训练模型，冻结前面的层
        if pretrained:
            self._freeze_backbone(freeze_layers=6)  # 冻结前6层
    
    def _freeze_backbone(self, freeze_layers=6):
        """
        冻结backbone的前几层
        
        Args:
            freeze_layers: 冻结的层数
        """
        layers = [
            self.backbone.conv1,
            self.backbone.bn1,
            self.backbone.layer1,
            self.backbone.layer2,
            self.backbone.layer3,
            self.backbone.layer4
        ]
        
        for i, layer in enumerate(layers[:freeze_layers]):
            for param in layer.parameters():
                param.requires_grad = False
        
        print(f"已冻结前 {freeze_layers} 层")
    
    def unfreeze_all(self):
        """解冻所有层"""
        for param in self.backbone.parameters():
            param.requires_grad = True
        print("已解冻所有层")
    
    def forward(self, x):
        """前向传播"""
        return self.backbone(x)
    
    def get_features(self, x):
        """
        获取特征向量（不包括最后的分类层）
        
        Args:
            x: 输入图像
        
        Returns:
            特征向量
        """
        # 通过backbone的所有层，除了最后的fc层
        x = self.backbone.conv1(x)
        x = self.backbone.bn1(x)
        x = self.backbone.relu(x)
        x = self.backbone.maxpool(x)
        
        x = self.backbone.layer1(x)
        x = self.backbone.layer2(x)
        x = self.backbone.layer3(x)
        x = self.backbone.layer4(x)
        
        x = self.backbone.avgpool(x)
        x = torch.flatten(x, 1)
        
        return x

class EfficientBirdNet(nn.Module):
    """
    基于EfficientNet的鸟类分类模型
    """
    
    def __init__(self, model_name='efficientnet_b0', num_classes=None, pretrained=True):
        super(EfficientBirdNet, self).__init__()
        
        if num_classes is None:
            num_classes = config.NUM_CLASSES
        
        # 加载EfficientNet
        if model_name == 'efficientnet_b0':
            self.backbone = models.efficientnet_b0(pretrained=pretrained)
        elif model_name == 'efficientnet_b1':
            self.backbone = models.efficientnet_b1(pretrained=pretrained)
        elif model_name == 'efficientnet_b2':
            self.backbone = models.efficientnet_b2(pretrained=pretrained)
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 替换分类器
        in_features = self.backbone.classifier[1].in_features
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(in_features, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        return self.backbone(x)

def create_model(model_type='resnet18', num_classes=None, pretrained=True):
    """
    创建模型
    
    Args:
        model_type: 模型类型
        num_classes: 类别数量
        pretrained: 是否使用预训练权重
    
    Returns:
        模型实例
    """
    if num_classes is None:
        num_classes = config.NUM_CLASSES
    
    if 'resnet' in model_type:
        return BirdResNet(model_type, num_classes, pretrained)
    elif 'efficientnet' in model_type:
        return EfficientBirdNet(model_type, num_classes, pretrained)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

def count_parameters(model):
    """计算模型参数数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def print_model_info(model):
    """打印模型信息"""
    total_params, trainable_params = count_parameters(model)
    print(f"模型总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    print(f"冻结参数数量: {total_params - trainable_params:,}")

if __name__ == "__main__":
    # 测试ResNet模型
    print("测试ResNet18模型:")
    model = BirdResNet('resnet18', pretrained=True)
    print_model_info(model)
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    # 测试特征提取
    features = model.get_features(x)
    print(f"特征形状: {features.shape}")
    
    print("\n" + "="*50)
    
    # 测试EfficientNet模型
    print("测试EfficientNet-B0模型:")
    model2 = EfficientBirdNet('efficientnet_b0', pretrained=True)
    print_model_info(model2)
    
    output2 = model2(x)
    print(f"输出形状: {output2.shape}")
