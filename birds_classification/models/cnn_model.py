# -*- coding: utf-8 -*-
"""
自定义CNN模型
实现基础的卷积神经网络架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from config import config

class CustomCNN(nn.Module):
    """
    自定义CNN模型
    包含3个卷积层和2个全连接层
    """
    
    def __init__(self, num_classes=None):
        super(CustomCNN, self).__init__()
        
        if num_classes is None:
            num_classes = config.NUM_CLASSES
        
        # 第一个卷积块
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(32)
        self.pool1 = nn.MaxPool2d(2, 2)  # 224x224 -> 112x112
        
        # 第二个卷积块
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        self.pool2 = nn.MaxPool2d(2, 2)  # 112x112 -> 56x56
        
        # 第三个卷积块
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(128)
        self.pool3 = nn.MaxPool2d(2, 2)  # 56x56 -> 28x28
        
        # 第四个卷积块
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.bn4 = nn.BatchNorm2d(256)
        self.pool4 = nn.MaxPool2d(2, 2)  # 28x28 -> 14x14
        
        # 自适应平均池化
        self.adaptive_pool = nn.AdaptiveAvgPool2d((7, 7))  # 输出固定为7x7
        
        # 全连接层
        self.fc1 = nn.Linear(256 * 7 * 7, 512)
        self.dropout1 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(512, 256)
        self.dropout2 = nn.Dropout(0.5)
        self.fc3 = nn.Linear(256, num_classes)
        
    def forward(self, x):
        """前向传播"""
        # 第一个卷积块
        x = self.pool1(F.relu(self.bn1(self.conv1(x))))
        
        # 第二个卷积块
        x = self.pool2(F.relu(self.bn2(self.conv2(x))))
        
        # 第三个卷积块
        x = self.pool3(F.relu(self.bn3(self.conv3(x))))
        
        # 第四个卷积块
        x = self.pool4(F.relu(self.bn4(self.conv4(x))))
        
        # 自适应池化
        x = self.adaptive_pool(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)
        
        return x
    
    def get_feature_maps(self, x):
        """
        获取特征图，用于可视化
        
        Args:
            x: 输入图像
        
        Returns:
            各层的特征图
        """
        features = {}
        
        # 第一层
        x = F.relu(self.bn1(self.conv1(x)))
        features['conv1'] = x
        x = self.pool1(x)
        
        # 第二层
        x = F.relu(self.bn2(self.conv2(x)))
        features['conv2'] = x
        x = self.pool2(x)
        
        # 第三层
        x = F.relu(self.bn3(self.conv3(x)))
        features['conv3'] = x
        x = self.pool3(x)
        
        # 第四层
        x = F.relu(self.bn4(self.conv4(x)))
        features['conv4'] = x
        
        return features

class SimpleCNN(nn.Module):
    """
    简化版CNN模型
    用于快速测试
    """
    
    def __init__(self, num_classes=None):
        super(SimpleCNN, self).__init__()
        
        if num_classes is None:
            num_classes = config.NUM_CLASSES
        
        # 卷积层
        self.conv1 = nn.Conv2d(3, 16, 3, padding=1)
        self.conv2 = nn.Conv2d(16, 32, 3, padding=1)
        self.conv3 = nn.Conv2d(32, 64, 3, padding=1)
        
        # 池化层
        self.pool = nn.MaxPool2d(2, 2)
        
        # 自适应池化
        self.adaptive_pool = nn.AdaptiveAvgPool2d((4, 4))
        
        # 全连接层
        self.fc1 = nn.Linear(64 * 4 * 4, 128)
        self.fc2 = nn.Linear(128, num_classes)
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        
        x = self.adaptive_pool(x)
        x = x.view(x.size(0), -1)
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x

def create_custom_cnn(model_type='custom', num_classes=None):
    """
    创建自定义CNN模型
    
    Args:
        model_type: 模型类型 ('custom' 或 'simple')
        num_classes: 类别数量
    
    Returns:
        CNN模型
    """
    if num_classes is None:
        num_classes = config.NUM_CLASSES
    
    if model_type == 'simple':
        return SimpleCNN(num_classes)
    else:
        return CustomCNN(num_classes)

def count_parameters(model):
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

if __name__ == "__main__":
    # 测试模型
    model = CustomCNN()
    print(f"模型参数数量: {count_parameters(model):,}")
    
    # 测试前向传播
    x = torch.randn(1, 3, 224, 224)
    output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    # 测试特征图
    features = model.get_feature_maps(x)
    for name, feature in features.items():
        print(f"{name}: {feature.shape}")
