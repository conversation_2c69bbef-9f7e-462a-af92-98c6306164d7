# -*- coding: utf-8 -*-
"""
推理预测模块
实现模型推理和预测功能
"""

import torch
import torch.nn.functional as F
from PIL import Image
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Union, Tuple
import json

from config import config
from utils.transforms import get_inference_transforms
from models.resnet_model import create_model
from models.cnn_model import create_custom_cnn

class BirdPredictor:
    """鸟类预测器"""
    
    def __init__(self, model_path: str = None, model_type: str = None, device: str = None):
        """
        初始化预测器
        
        Args:
            model_path: 模型权重路径
            model_type: 模型类型
            device: 设备
        """
        self.device = torch.device(device if device else 
                                 ('cuda' if torch.cuda.is_available() else 'cpu'))
        
        # 加载模型
        self.model = self._load_model(model_path, model_type)
        self.model.eval()
        
        # 数据变换
        self.transform = get_inference_transforms()
        
        # 类别信息
        self.class_names = config.CLASS_NAMES
        self.class_to_idx = config.get_class_to_idx()
        self.idx_to_class = config.get_idx_to_class()
        
        print(f"预测器初始化完成，设备: {self.device}")
    
    def _load_model(self, model_path: str = None, model_type: str = None):
        """
        加载模型
        
        Args:
            model_path: 模型路径
            model_type: 模型类型
        
        Returns:
            加载的模型
        """
        if model_path is None:
            model_path = config.WEIGHTS_DIR / 'best_model.pth'
        
        if not Path(model_path).exists():
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 加载检查点
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 获取模型配置
        model_config = checkpoint.get('config', {})
        model_type = model_type or model_config.get('model_type', config.MODEL_TYPE)
        num_classes = model_config.get('num_classes', config.NUM_CLASSES)
        
        # 创建模型
        if 'resnet' in model_type or 'efficientnet' in model_type:
            model = create_model(model_type, num_classes, pretrained=False)
        else:
            model = create_custom_cnn('custom', num_classes)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        
        print(f"成功加载模型: {model_type}")
        return model
    
    def preprocess_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> torch.Tensor:
        """
        预处理图像
        
        Args:
            image_input: 图像输入（路径、numpy数组或PIL图像）
        
        Returns:
            预处理后的张量
        """
        # 统一转换为PIL图像
        if isinstance(image_input, str):
            # 文件路径
            image = Image.open(image_input).convert('RGB')
        elif isinstance(image_input, np.ndarray):
            # numpy数组
            if image_input.shape[-1] == 3:  # BGR to RGB
                image_input = cv2.cvtColor(image_input, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(image_input)
        elif isinstance(image_input, Image.Image):
            # PIL图像
            image = image_input.convert('RGB')
        else:
            raise ValueError(f"不支持的图像类型: {type(image_input)}")
        
        # 应用变换
        tensor = self.transform(image)
        
        # 添加batch维度
        tensor = tensor.unsqueeze(0)
        
        return tensor
    
    def predict(self, image_input: Union[str, np.ndarray, Image.Image], 
                top_k: int = 5) -> Dict:
        """
        预测单张图像
        
        Args:
            image_input: 图像输入
            top_k: 返回前k个预测结果
        
        Returns:
            预测结果字典
        """
        # 预处理图像
        image_tensor = self.preprocess_image(image_input)
        image_tensor = image_tensor.to(self.device)
        
        with torch.no_grad():
            # 前向传播
            outputs = self.model(image_tensor)
            
            # 计算概率
            probabilities = F.softmax(outputs, dim=1)
            
            # 获取top-k预测
            top_probs, top_indices = torch.topk(probabilities[0], 
                                              min(top_k, len(self.class_names)))
            
            # 构建结果
            predictions = []
            for i in range(len(top_probs)):
                class_idx = top_indices[i].item()
                prob = top_probs[i].item()
                class_name = self.idx_to_class[class_idx]
                
                predictions.append({
                    'class_name': class_name,
                    'class_index': class_idx,
                    'probability': prob,
                    'confidence': prob * 100
                })
            
            # 最佳预测
            best_prediction = predictions[0]
            
            result = {
                'predicted_class': best_prediction['class_name'],
                'predicted_index': best_prediction['class_index'],
                'confidence': best_prediction['confidence'],
                'probability': best_prediction['probability'],
                'top_predictions': predictions
            }
            
            return result
    
    def predict_batch(self, image_list: List[Union[str, np.ndarray, Image.Image]], 
                     top_k: int = 5) -> List[Dict]:
        """
        批量预测
        
        Args:
            image_list: 图像列表
            top_k: 返回前k个预测结果
        
        Returns:
            预测结果列表
        """
        results = []
        
        for i, image_input in enumerate(image_list):
            try:
                result = self.predict(image_input, top_k)
                result['index'] = i
                results.append(result)
            except Exception as e:
                print(f"预测第{i}张图像时出错: {e}")
                results.append({
                    'index': i,
                    'error': str(e),
                    'predicted_class': None,
                    'confidence': 0.0
                })
        
        return results
    
    def predict_from_folder(self, folder_path: str, 
                           extensions: List[str] = None,
                           top_k: int = 5) -> Dict:
        """
        预测文件夹中的所有图像
        
        Args:
            folder_path: 文件夹路径
            extensions: 支持的文件扩展名
            top_k: 返回前k个预测结果
        
        Returns:
            预测结果字典
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        folder_path = Path(folder_path)
        if not folder_path.exists():
            raise FileNotFoundError(f"文件夹不存在: {folder_path}")
        
        # 收集图像文件
        image_files = []
        for ext in extensions:
            image_files.extend(folder_path.glob(f"*{ext}"))
            image_files.extend(folder_path.glob(f"*{ext.upper()}"))
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 批量预测
        results = []
        for image_file in image_files:
            try:
                result = self.predict(str(image_file), top_k)
                result['filename'] = image_file.name
                result['filepath'] = str(image_file)
                results.append(result)
            except Exception as e:
                print(f"预测 {image_file.name} 时出错: {e}")
        
        # 统计结果
        class_counts = {}
        for result in results:
            if 'predicted_class' in result and result['predicted_class']:
                class_name = result['predicted_class']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        summary = {
            'total_images': len(image_files),
            'successful_predictions': len(results),
            'class_distribution': class_counts,
            'results': results
        }
        
        return summary
    
    def visualize_prediction(self, image_input: Union[str, np.ndarray, Image.Image],
                           save_path: str = None) -> Dict:
        """
        可视化预测结果
        
        Args:
            image_input: 图像输入
            save_path: 保存路径
        
        Returns:
            预测结果
        """
        import matplotlib.pyplot as plt
        
        # 预测
        result = self.predict(image_input, top_k=5)
        
        # 加载原始图像
        if isinstance(image_input, str):
            image = Image.open(image_input).convert('RGB')
        elif isinstance(image_input, np.ndarray):
            if image_input.shape[-1] == 3:
                image_input = cv2.cvtColor(image_input, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(image_input)
        else:
            image = image_input.convert('RGB')
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 显示图像
        ax1.imshow(image)
        ax1.set_title(f'预测结果: {result["predicted_class"]}\n'
                     f'置信度: {result["confidence"]:.1f}%', 
                     fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # 显示top-5预测
        top_predictions = result['top_predictions']
        classes = [pred['class_name'] for pred in top_predictions]
        probs = [pred['probability'] for pred in top_predictions]
        
        bars = ax2.barh(range(len(classes)), probs, alpha=0.7)
        ax2.set_yticks(range(len(classes)))
        ax2.set_yticklabels(classes)
        ax2.set_xlabel('概率')
        ax2.set_title('Top-5 预测结果')
        ax2.set_xlim(0, 1)
        
        # 添加概率标签
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            ax2.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                    f'{prob:.3f}', va='center', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测结果保存到: {save_path}")
        
        plt.show()
        
        return result

def load_predictor(model_path: str = None, model_type: str = None, device: str = None):
    """
    加载预测器
    
    Args:
        model_path: 模型路径
        model_type: 模型类型
        device: 设备
    
    Returns:
        预测器实例
    """
    return BirdPredictor(model_path, model_type, device)
