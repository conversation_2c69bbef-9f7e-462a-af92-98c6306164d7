# -*- coding: utf-8 -*-
"""
ONNX推理模块
实现ONNX模型的导出和推理
"""

import torch
import onnx
import onnxruntime as ort
import numpy as np
from PIL import Image
import cv2
from pathlib import Path
from typing import Dict, List, Union, Tuple
import json

from config import config
from utils.transforms import get_inference_transforms

class ONNXPredictor:
    """ONNX预测器"""
    
    def __init__(self, onnx_model_path: str = None, providers: List[str] = None):
        """
        初始化ONNX预测器
        
        Args:
            onnx_model_path: ONNX模型路径
            providers: 执行提供者
        """
        if onnx_model_path is None:
            onnx_model_path = config.ONNX_MODEL_PATH
        
        if not Path(onnx_model_path).exists():
            raise FileNotFoundError(f"ONNX模型文件不存在: {onnx_model_path}")
        
        # 设置执行提供者
        if providers is None:
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        
        # 创建推理会话
        self.session = ort.InferenceSession(str(onnx_model_path), providers=providers)
        
        # 获取输入输出信息
        self.input_name = self.session.get_inputs()[0].name
        self.output_name = self.session.get_outputs()[0].name
        self.input_shape = self.session.get_inputs()[0].shape
        
        # 数据变换
        self.transform = get_inference_transforms()
        
        # 类别信息
        self.class_names = config.CLASS_NAMES
        self.class_to_idx = config.get_class_to_idx()
        self.idx_to_class = config.get_idx_to_class()
        
        print(f"ONNX预测器初始化完成")
        print(f"输入形状: {self.input_shape}")
        print(f"执行提供者: {self.session.get_providers()}")
    
    def preprocess_image(self, image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """
        预处理图像
        
        Args:
            image_input: 图像输入
        
        Returns:
            预处理后的numpy数组
        """
        # 统一转换为PIL图像
        if isinstance(image_input, str):
            image = Image.open(image_input).convert('RGB')
        elif isinstance(image_input, np.ndarray):
            if image_input.shape[-1] == 3:
                image_input = cv2.cvtColor(image_input, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(image_input)
        elif isinstance(image_input, Image.Image):
            image = image_input.convert('RGB')
        else:
            raise ValueError(f"不支持的图像类型: {type(image_input)}")
        
        # 应用变换
        tensor = self.transform(image)
        
        # 转换为numpy数组并添加batch维度
        numpy_array = tensor.unsqueeze(0).numpy()
        
        return numpy_array
    
    def predict(self, image_input: Union[str, np.ndarray, Image.Image], 
                top_k: int = 5) -> Dict:
        """
        预测单张图像
        
        Args:
            image_input: 图像输入
            top_k: 返回前k个预测结果
        
        Returns:
            预测结果字典
        """
        # 预处理图像
        input_array = self.preprocess_image(image_input)
        
        # ONNX推理
        outputs = self.session.run([self.output_name], {self.input_name: input_array})
        logits = outputs[0][0]  # 获取第一个样本的输出
        
        # 计算概率
        exp_logits = np.exp(logits - np.max(logits))  # 数值稳定的softmax
        probabilities = exp_logits / np.sum(exp_logits)
        
        # 获取top-k预测
        top_indices = np.argsort(probabilities)[::-1][:top_k]
        
        # 构建结果
        predictions = []
        for idx in top_indices:
            prob = probabilities[idx]
            class_name = self.idx_to_class[idx]
            
            predictions.append({
                'class_name': class_name,
                'class_index': int(idx),
                'probability': float(prob),
                'confidence': float(prob * 100)
            })
        
        # 最佳预测
        best_prediction = predictions[0]
        
        result = {
            'predicted_class': best_prediction['class_name'],
            'predicted_index': best_prediction['class_index'],
            'confidence': best_prediction['confidence'],
            'probability': best_prediction['probability'],
            'top_predictions': predictions
        }
        
        return result
    
    def predict_batch(self, image_list: List[Union[str, np.ndarray, Image.Image]], 
                     top_k: int = 5) -> List[Dict]:
        """
        批量预测
        
        Args:
            image_list: 图像列表
            top_k: 返回前k个预测结果
        
        Returns:
            预测结果列表
        """
        results = []
        
        for i, image_input in enumerate(image_list):
            try:
                result = self.predict(image_input, top_k)
                result['index'] = i
                results.append(result)
            except Exception as e:
                print(f"预测第{i}张图像时出错: {e}")
                results.append({
                    'index': i,
                    'error': str(e),
                    'predicted_class': None,
                    'confidence': 0.0
                })
        
        return results

def export_to_onnx(model, model_path: str = None, onnx_path: str = None, 
                   input_shape: Tuple = None):
    """
    将PyTorch模型导出为ONNX格式
    
    Args:
        model: PyTorch模型
        model_path: PyTorch模型路径
        onnx_path: ONNX保存路径
        input_shape: 输入形状
    """
    if model_path:
        # 加载模型
        checkpoint = torch.load(model_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    
    if input_shape is None:
        input_shape = (1, 3, 224, 224)
    
    if onnx_path is None:
        onnx_path = config.ONNX_MODEL_PATH
    
    # 创建虚拟输入
    dummy_input = torch.randn(input_shape)
    
    # 导出ONNX
    torch.onnx.export(
        model,
        dummy_input,
        onnx_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    print(f"模型已导出为ONNX格式: {onnx_path}")
    
    # 验证ONNX模型
    try:
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX模型验证通过")
    except Exception as e:
        print(f"ONNX模型验证失败: {e}")
    
    return onnx_path

def compare_pytorch_onnx(pytorch_model, onnx_path: str, test_input: torch.Tensor = None):
    """
    比较PyTorch和ONNX模型的输出
    
    Args:
        pytorch_model: PyTorch模型
        onnx_path: ONNX模型路径
        test_input: 测试输入
    
    Returns:
        比较结果
    """
    if test_input is None:
        test_input = torch.randn(1, 3, 224, 224)
    
    # PyTorch推理
    pytorch_model.eval()
    with torch.no_grad():
        pytorch_output = pytorch_model(test_input)
    
    # ONNX推理
    session = ort.InferenceSession(onnx_path)
    input_name = session.get_inputs()[0].name
    onnx_output = session.run(None, {input_name: test_input.numpy()})
    
    # 比较输出
    pytorch_result = pytorch_output.numpy()
    onnx_result = onnx_output[0]
    
    # 计算差异
    max_diff = np.max(np.abs(pytorch_result - onnx_result))
    mean_diff = np.mean(np.abs(pytorch_result - onnx_result))
    
    print(f"PyTorch vs ONNX 输出比较:")
    print(f"最大差异: {max_diff}")
    print(f"平均差异: {mean_diff}")
    print(f"是否近似相等: {np.allclose(pytorch_result, onnx_result, atol=1e-5)}")
    
    return {
        'pytorch_output': pytorch_result,
        'onnx_output': onnx_result,
        'max_diff': max_diff,
        'mean_diff': mean_diff,
        'is_close': np.allclose(pytorch_result, onnx_result, atol=1e-5)
    }

def benchmark_onnx_inference(onnx_path: str, num_runs: int = 100):
    """
    ONNX推理性能基准测试
    
    Args:
        onnx_path: ONNX模型路径
        num_runs: 运行次数
    
    Returns:
        性能统计
    """
    import time
    
    # 创建会话
    session = ort.InferenceSession(onnx_path)
    input_name = session.get_inputs()[0].name
    
    # 创建测试输入
    test_input = np.random.randn(1, 3, 224, 224).astype(np.float32)
    
    # 预热
    for _ in range(10):
        session.run(None, {input_name: test_input})
    
    # 性能测试
    times = []
    for _ in range(num_runs):
        start_time = time.time()
        session.run(None, {input_name: test_input})
        end_time = time.time()
        times.append(end_time - start_time)
    
    # 统计结果
    times = np.array(times)
    stats = {
        'mean_time': np.mean(times),
        'std_time': np.std(times),
        'min_time': np.min(times),
        'max_time': np.max(times),
        'fps': 1.0 / np.mean(times)
    }
    
    print(f"ONNX推理性能统计 ({num_runs} 次运行):")
    print(f"平均时间: {stats['mean_time']*1000:.2f} ms")
    print(f"标准差: {stats['std_time']*1000:.2f} ms")
    print(f"最小时间: {stats['min_time']*1000:.2f} ms")
    print(f"最大时间: {stats['max_time']*1000:.2f} ms")
    print(f"FPS: {stats['fps']:.2f}")
    
    return stats
