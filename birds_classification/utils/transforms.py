# -*- coding: utf-8 -*-
"""
数据增强和预处理模块
实现训练和验证阶段的不同数据变换
"""

import torch
from torchvision import transforms
from config import config

def get_train_transforms():
    """
    获取训练阶段的数据增强变换
    包含随机翻转、旋转、裁剪、颜色调整等
    """
    return transforms.Compose([
        transforms.Resize((256, 256)),  # 先放大到256x256
        transforms.RandomResizedCrop(
            config.IMAGE_SIZE[0], 
            scale=config.SCALE_RANGE
        ),  # 随机裁剪到224x224
        transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
        transforms.RandomRotation(config.ROTATION_DEGREES),  # 随机旋转
        transforms.ColorJitter(
            brightness=config.BRIGHTNESS,
            contrast=config.CONTRAST,
            saturation=config.SATURATION,
            hue=config.HUE
        ),  # 随机调整颜色
        transforms.ToTensor(),  # 转换为Tensor
        transforms.Normalize(
            mean=config.MEAN,
            std=config.STD
        )  # 标准化
    ])

def get_val_transforms():
    """
    获取验证/测试阶段的数据变换
    只进行基本的尺寸调整和标准化
    """
    return transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.CenterCrop(config.IMAGE_SIZE[0]),  # 中心裁剪
        transforms.ToTensor(),
        transforms.Normalize(
            mean=config.MEAN,
            std=config.STD
        )
    ])

def get_inference_transforms():
    """
    获取推理阶段的数据变换
    与验证变换相同
    """
    return get_val_transforms()

def denormalize_tensor(tensor, mean=None, std=None):
    """
    反标准化张量，用于可视化
    
    Args:
        tensor: 标准化后的张量
        mean: 均值
        std: 标准差
    
    Returns:
        反标准化后的张量
    """
    if mean is None:
        mean = config.MEAN
    if std is None:
        std = config.STD
    
    mean = torch.tensor(mean).view(3, 1, 1)
    std = torch.tensor(std).view(3, 1, 1)
    
    return tensor * std + mean

def tensor_to_pil(tensor):
    """
    将张量转换为PIL图像
    
    Args:
        tensor: 图像张量 (C, H, W)
    
    Returns:
        PIL图像
    """
    # 反标准化
    tensor = denormalize_tensor(tensor)
    # 限制到[0,1]范围
    tensor = torch.clamp(tensor, 0, 1)
    # 转换为PIL
    transform = transforms.ToPILImage()
    return transform(tensor)
