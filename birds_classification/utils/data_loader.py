# -*- coding: utf-8 -*-
"""
数据加载模块
实现数据集类和数据加载器创建
"""

import os
import json
import random
from pathlib import Path
from typing import Tuple, Dict, List
import shutil

import torch
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import pandas as pd

from config import config
from .transforms import get_train_transforms, get_val_transforms

class BirdDataset(Dataset):
    """鸟类数据集类"""
    
    def __init__(self, data_list: List[Dict], transform=None):
        """
        初始化数据集
        
        Args:
            data_list: 包含图片路径和标签的列表
            transform: 数据变换
        """
        self.data_list = data_list
        self.transform = transform
        self.class_to_idx = config.get_class_to_idx()
        
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        """获取单个样本"""
        item = self.data_list[idx]
        image_path = item['image_path']
        label = item['label']
        
        # 加载图像
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            # 返回一个默认的黑色图像
            image = Image.new('RGB', config.IMAGE_SIZE, (0, 0, 0))
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        # 转换标签为索引
        label_idx = self.class_to_idx[label]
        
        return image, label_idx

def split_dataset():
    """
    分割数据集为训练、验证、测试集
    
    Returns:
        train_data, val_data, test_data: 三个数据列表
    """
    print("开始分割数据集...")
    
    # 收集所有图片信息
    all_data = []
    raw_data_path = Path(config.RAW_DATA_PATH)
    
    for class_name in config.CLASS_NAMES:
        class_dir = raw_data_path / class_name
        if not class_dir.exists():
            print(f"警告: 类别目录 {class_dir} 不存在")
            continue
            
        # 获取该类别的所有图片
        image_files = list(class_dir.glob("*.jpg")) + list(class_dir.glob("*.png"))
        print(f"类别 {class_name}: 找到 {len(image_files)} 张图片")
        
        for image_path in image_files:
            all_data.append({
                'image_path': str(image_path),
                'label': class_name
            })
    
    print(f"总共找到 {len(all_data)} 张图片")
    
    # 按类别分组
    class_data = {}
    for item in all_data:
        label = item['label']
        if label not in class_data:
            class_data[label] = []
        class_data[label].append(item)
    
    # 为每个类别分别分割数据
    train_data = []
    val_data = []
    test_data = []
    
    for class_name, class_items in class_data.items():
        # 随机打乱
        random.shuffle(class_items)
        
        n_total = len(class_items)
        n_train = int(n_total * config.TRAIN_RATIO)
        n_val = int(n_total * config.VAL_RATIO)
        
        # 分割
        train_items = class_items[:n_train]
        val_items = class_items[n_train:n_train + n_val]
        test_items = class_items[n_train + n_val:]
        
        train_data.extend(train_items)
        val_data.extend(val_items)
        test_data.extend(test_items)
        
        print(f"{class_name}: 训练={len(train_items)}, 验证={len(val_items)}, 测试={len(test_items)}")
    
    # 再次打乱整个数据集
    random.shuffle(train_data)
    random.shuffle(val_data)
    random.shuffle(test_data)
    
    print(f"最终分割结果: 训练={len(train_data)}, 验证={len(val_data)}, 测试={len(test_data)}")
    
    return train_data, val_data, test_data

def save_data_splits(train_data, val_data, test_data):
    """保存数据分割结果"""
    config.create_directories()
    
    # 保存为JSON文件
    splits = {
        'train': train_data,
        'val': val_data,
        'test': test_data
    }
    
    for split_name, data in splits.items():
        split_file = config.SPLITS_DATA_PATH / f"{split_name}.json"
        with open(split_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"保存 {split_name} 数据到 {split_file}")

def load_data_splits():
    """加载数据分割结果"""
    splits = {}
    for split_name in ['train', 'val', 'test']:
        split_file = config.SPLITS_DATA_PATH / f"{split_name}.json"
        if split_file.exists():
            with open(split_file, 'r', encoding='utf-8') as f:
                splits[split_name] = json.load(f)
        else:
            return None
    return splits['train'], splits['val'], splits['test']

def create_data_loaders(batch_size=None):
    """
    创建数据加载器
    
    Args:
        batch_size: 批次大小
    
    Returns:
        train_loader, val_loader, test_loader
    """
    if batch_size is None:
        batch_size = config.BATCH_SIZE
    
    # 尝试加载已有的数据分割
    data_splits = load_data_splits()
    if data_splits is None:
        # 如果没有已保存的分割，则重新分割
        train_data, val_data, test_data = split_dataset()
        save_data_splits(train_data, val_data, test_data)
    else:
        train_data, val_data, test_data = data_splits
        print("加载已保存的数据分割")
    
    # 创建数据集
    train_dataset = BirdDataset(train_data, transform=get_train_transforms())
    val_dataset = BirdDataset(val_data, transform=get_val_transforms())
    test_dataset = BirdDataset(test_data, transform=get_val_transforms())
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    print(f"数据加载器创建完成:")
    print(f"  训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    print(f"  验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    print(f"  测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    return train_loader, val_loader, test_loader
