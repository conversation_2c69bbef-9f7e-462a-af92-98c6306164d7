# -*- coding: utf-8 -*-
"""
评估指标模块
实现准确率、精确度、召回率等指标计算
"""

import numpy as np
import torch
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import confusion_matrix, classification_report
import pandas as pd
from typing import List, Dict, Tuple

from config import config

class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置所有指标"""
        self.all_predictions = []
        self.all_targets = []
        self.running_loss = 0.0
        self.num_samples = 0
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor, loss: float = 0.0):
        """
        更新指标
        
        Args:
            predictions: 预测结果 (batch_size, num_classes)
            targets: 真实标签 (batch_size,)
            loss: 损失值
        """
        # 获取预测类别
        pred_classes = torch.argmax(predictions, dim=1)
        
        # 转换为numpy数组
        pred_classes = pred_classes.cpu().numpy()
        targets = targets.cpu().numpy()
        
        # 添加到列表
        self.all_predictions.extend(pred_classes.tolist())
        self.all_targets.extend(targets.tolist())
        
        # 更新损失
        self.running_loss += loss * len(targets)
        self.num_samples += len(targets)
    
    def compute_metrics(self) -> Dict[str, float]:
        """
        计算所有指标
        
        Returns:
            包含各种指标的字典
        """
        if len(self.all_predictions) == 0:
            return {}
        
        # 转换为numpy数组
        y_true = np.array(self.all_targets)
        y_pred = np.array(self.all_predictions)
        
        # 计算指标
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
        f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
        
        # 平均损失
        avg_loss = self.running_loss / self.num_samples if self.num_samples > 0 else 0.0
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'loss': avg_loss,
            'num_samples': self.num_samples
        }
    
    def get_confusion_matrix(self) -> np.ndarray:
        """获取混淆矩阵"""
        if len(self.all_predictions) == 0:
            return np.array([])
        
        y_true = np.array(self.all_targets)
        y_pred = np.array(self.all_predictions)
        
        return confusion_matrix(y_true, y_pred)
    
    def get_classification_report(self) -> str:
        """获取分类报告"""
        if len(self.all_predictions) == 0:
            return ""
        
        y_true = np.array(self.all_targets)
        y_pred = np.array(self.all_predictions)
        
        return classification_report(
            y_true, y_pred, 
            target_names=config.CLASS_NAMES,
            zero_division=0
        )

def calculate_metrics(predictions: List[int], targets: List[int]) -> Dict[str, float]:
    """
    计算评估指标
    
    Args:
        predictions: 预测结果列表
        targets: 真实标签列表
    
    Returns:
        包含各种指标的字典
    """
    y_true = np.array(targets)
    y_pred = np.array(predictions)
    
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
    recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
    f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }

def save_metrics_to_excel(metrics_dict: Dict, confusion_mat: np.ndarray, 
                         save_path: str):
    """
    将指标保存到Excel文件
    
    Args:
        metrics_dict: 指标字典
        confusion_mat: 混淆矩阵
        save_path: 保存路径
    """
    with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
        # 保存总体指标
        metrics_df = pd.DataFrame([metrics_dict])
        metrics_df.to_excel(writer, sheet_name='Overall_Metrics', index=False)
        
        # 保存混淆矩阵
        if confusion_mat.size > 0:
            confusion_df = pd.DataFrame(
                confusion_mat,
                index=config.CLASS_NAMES,
                columns=config.CLASS_NAMES
            )
            confusion_df.to_excel(writer, sheet_name='Confusion_Matrix')
        
        # 保存每个类别的指标
        if confusion_mat.size > 0:
            class_metrics = []
            for i, class_name in enumerate(config.CLASS_NAMES):
                tp = confusion_mat[i, i]
                fp = confusion_mat[:, i].sum() - tp
                fn = confusion_mat[i, :].sum() - tp
                tn = confusion_mat.sum() - tp - fp - fn
                
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                class_metrics.append({
                    'Class': class_name,
                    'Precision': precision,
                    'Recall': recall,
                    'F1_Score': f1,
                    'True_Positive': tp,
                    'False_Positive': fp,
                    'False_Negative': fn,
                    'True_Negative': tn
                })
            
            class_df = pd.DataFrame(class_metrics)
            class_df.to_excel(writer, sheet_name='Class_Metrics', index=False)
    
    print(f"指标已保存到: {save_path}")

def print_metrics(metrics_dict: Dict[str, float]):
    """打印指标"""
    print("\n" + "="*50)
    print("模型评估指标")
    print("="*50)
    print(f"准确率 (Accuracy): {metrics_dict.get('accuracy', 0):.4f}")
    print(f"精确度 (Precision): {metrics_dict.get('precision', 0):.4f}")
    print(f"召回率 (Recall): {metrics_dict.get('recall', 0):.4f}")
    print(f"F1分数 (F1-Score): {metrics_dict.get('f1_score', 0):.4f}")
    if 'loss' in metrics_dict:
        print(f"平均损失 (Loss): {metrics_dict['loss']:.4f}")
    print("="*50)
