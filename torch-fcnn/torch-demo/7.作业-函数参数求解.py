import torch


def test01():
    # 定义数据
    x = torch.tensor([1, 2, 3, 4, 5], dtype=torch.float)
    y = torch.tensor([3, 5, 7, 9, 11], dtype=torch.float)

    # 随机初始化叶子节点
    w = torch.tensor(1.0, requires_grad=True)
    b = torch.tensor(0.0, requires_grad=True)

    epochs = 1000
    lr = 0.02

    for epoch in range(epochs):
        y_pred = w * x + b

        loss = ((y_pred - y) ** 2).mean()

        # 梯度清零
        if w.grad is not None and b.grad is not None:
            w.grad.zero_()
            b.grad.zero_()

        # 反向传播
        loss.backward()

        # 更新参数
        with torch.no_grad():
            w -= lr * w.grad
            b -= lr * b.grad

        print(f'epoch:{epoch},loss:{loss.item()}')

    print(f'w:{w.item()},b:{b.item()}')

if __name__ == '__main__':
    test01()
