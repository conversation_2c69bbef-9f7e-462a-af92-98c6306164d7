import torch
from matplotlib import pyplot as plt


# 自动求导：
# 1.叶子节点张量要添加requires_grad=True
# 2.叶子节点的数据类型是浮点数
# 3.调用backward()，可以自动求导
# 4.求导后的梯度保存在叶子节点的grad属性中
def test01():
    x = torch.tensor(1.0, requires_grad=True)

    y = x ** 2
    # 反向传播，做自动求导
    y.backward()

    print(x.grad)


def test02():
    x = torch.tensor([1, 2, 3], requires_grad=True, dtype=torch.float32)

    y = x ** 2

    # 1.梯度向量，用来对一维向量进行反向传播
    # y.backward(torch.tensor([1.0, 1.0, 1.0]))

    # 2.把梯度向量通过类似计算损失的方式将输出转换为标量，然后再调用backward()，常用的方式
    z = y.sum()

    z.backward()

    print(x.grad)


# 中间变量在反向传播中也会参与梯度计算，但是在计算完成后将梯度清除
def test03():
    x = torch.tensor([1, 2, 3], requires_grad=True, dtype=torch.float32)
    y = torch.tensor([2, 3, 4], requires_grad=True, dtype=torch.float32)

    z = x * y
    print(z.requires_grad)

    loss = z.sum()

    loss.backward()

    print(x.grad, y.grad)
    print(z.grad)


def test04():
    x = torch.tensor([1, 2, 3], requires_grad=True, dtype=torch.float32)
    # torch.no_grad():禁止该上下文的代码参与梯度计算
    with torch.no_grad():
        y = x ** 2

    print(y.requires_grad)


def test05():
    x = torch.tensor([1, 2, 3], requires_grad=True, dtype=torch.float32)

    # x和y映射函数要放在循环内部
    # 计算图中叶子节点的梯度默认是累加的
    # 不希望叶子节点的梯度累加，需要对每轮次的梯度进行清零
    for epoch in range(3):
        y = x ** 2
        z = y.sum()

        # 梯度清零，一般在backward()前执行
        if x.grad is not None:
            x.grad.zero_()

        z.backward()

        print(x.grad)


# 求函数最小值
def test06():
    x = torch.tensor(3.0, requires_grad=True)

    x_list = []
    y_list = []

    epochs = 500
    lr = 0.1

    for epoch in range(epochs):

        y = x ** 2

        z = y.sum()

        # 梯度清零
        if x.grad is not None:
            x.grad.zero_()

        z.backward()

        # 在通过梯度下降公式进行参数更新时，不参与梯度计算
        # 计算图中的叶子节点不允许直接修改值，需要通过torch.no_grad()上下文控制器进行更新x的值
        with torch.no_grad():
            x -= lr * x.grad

        x_list.append(x.item())
        y_list.append(y.item())

    plt.scatter(x_list, y_list)
    plt.show()


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    # test04()
    # test05()
    test06()
