import torch


# 使用tensor()创建张量，可以创建标量、一维、二维、多维数组
# 参数：
# dtype:指定张量的数据类型
# device：指定张量运算的设备：cuda、cpu，如果不指定，默认在cpu上运算
def test01():
    t1 = torch.tensor([1, 2, 3], dtype=torch.float32, device='cpu')
    print(t1)
    print(t1.shape)
    # size()和shape作用一样，获取张量的形状
    print(t1.size())
    # dtype:获取张量的数据类型，如果在创建张量时没有指定dtype，则自动根据输入数组的数据类型判断
    print(t1.dtype)


# 使用Tensor()构造函数创建张量
# 强制将数据类型转换为float32
# 没有dtype和device属性
# tensor()创建张量更灵活，使用更多一些
def test02():
    t1 = torch.Tensor([1, 2, 3])
    print(t1)
    print(t1.shape)
    print(t1.dtype)


if __name__ == '__main__':
    test02()
