import torch
import numpy as np


# tensor转numpy:numpy()
def test01():
    t1 = torch.tensor([[1, 2, 3], [4, 5, 6]])
    print(t1)
    # 浅拷贝，数据内存共享
    n1 = t1.numpy()
    print(n1)

    # 深拷贝，创建新的副本
    n2 = t1.numpy().copy()
    print(n2)


# numpy转换为tensor
# 浅拷贝：torch.from_numpy()，数据内存共享
# 深拷贝：torch.tensor()，参数为numpy数组，创建新的副本
def test02():
    n1 = np.array([1, 2, 3, 4, 5])
    t1 = torch.from_numpy(n1)
    print(t1)

    t2 = torch.tensor(n1)
    print(t2)


if __name__ == '__main__':
    # test01()
    test02()
