import torch


# item():从单个元素的tensor中获取标量值
# 获取元素和tensor的维度没有关系，只要tensor只有一个元素，都可以使用该方法获取值
def test01():
    t1 = torch.tensor(18)
    print(t1.item())

    t2 = torch.tensor([[19]])
    print(t2.item())


# tansor运算函数：如果函数名后带有下划线则表示该方法为原地修改，否则表示不修改原始值，而是返回一个新对象

# 阿达码积：两个相同形状的矩阵相同位置的元素相乘，得出的新矩阵，运算符号：mul或*
# 矩阵相乘：第一个矩阵的行与第二个矩阵的所有列相乘后得到行数据，(m,p)和(p,n)形状的矩阵相乘，结果为(m,n)，运算符号：matmul或@
def test02():
    t1 = torch.tensor([[2, 3], [4, 5]])
    t2 = torch.tensor([[1, 2], [3, 4]])
    # 阿达码积
    print(t1 * t2)

    # 矩阵相乘
    print(t1 @ t2)


# view:张量变形，和reshape()类似
# 和reshape区别：
# view()：前提是数据在内存中是连续的，即按C顺序存储
# view效率比reshape高
# 如果数据不连续，使用view()会报错，可以使用reshape代替
def test03():
    t1 = torch.tensor([[1, 2, 3], [4, 5, 6]])
    print(t1.is_contiguous())
    t2 = t1.view(3, -1)
    print(t2.is_contiguous())

    t3 = t1.t()
    print(t3.is_contiguous())

    t4 = t3.view(2, -1)
    print(t4)


# 交换张量的维度
# transpose():交换张量的两个维度
# permute()：交换张量的任意个维度

def test04():
    t1 = torch.randint(0, 10, (2, 3, 4))
    print(t1)

    t2 = torch.permute(t1, (1, 2, 0))
    print(t2.shape)
    print(t2)


# 升维和降维
# 升维：unsqueeze()
# 降维: squeeze()
# 使用场景：一般在图形图像处理上，添加或删除维度
def test05():
    t1 = torch.randint(1, 10, (2, 3, 4))
    # 升维
    t2 = torch.unsqueeze(t1, dim=0)
    print(t2.size())
    # 升维方式2
    t2 = t1.unsqueeze(0)
    print(t2.size())

    # 降维
    # 1.如果不指定dim，默认删除所有维度数为1的维度
    # 2.如果指定dim的维度数不为1，则不做任何操作，也不报错
    t3 = torch.squeeze(t2, dim=1)
    print(t3.size())


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    # test04()
    test05()
