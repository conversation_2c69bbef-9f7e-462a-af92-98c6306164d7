import torch


def test01():
    # 在创建张量时指定device
    t1 = torch.tensor([1, 2, 3], device='cuda')
    print(t1)

    # 使用to()转换运算设备，转换后要重新赋值变量
    t2 = torch.tensor([1, 2, 3])
    t2 = t2.to('cuda')
    print(t2.device)

    # 使用cuda()或cpu()切换运算设备，转换后要重新赋值变量
    t3 = torch.tensor([1, 2, 3], device='cuda')
    t3 = t3.cpu()
    print(t3.device)
    t3 = t3.cuda()
    print(t3.device)




if __name__ == '__main__':
    test01()
