import torch
from torch import nn
from torch.utils.data import TensorDataset, DataLoader
import pandas as pd
from torchvision import datasets, transforms


def build_csv_data(filepath):
    df = pd.read_csv(filepath)
    df.drop(['学号', '姓名'], axis=1, inplace=True)

    samples = df.iloc[..., :-1]
    labels = df.iloc[..., -1]

    samples = torch.tensor(samples.values)
    labels = torch.tensor(labels.values)

    return samples, labels


def load_csv_data():
    filepath = 'datasets/大数据答辩成绩表.csv'
    samples, labels = build_csv_data(filepath)

    dataset = TensorDataset(samples, labels)

    dataloader = DataLoader(
        dataset=dataset,
        batch_size=1,
        shuffle=True
    )

    for sample, label in dataloader:
        print(sample)
        print(label)
        break


def load_img_data():
    path = 'datasets/animals'

    transform = transforms.Compose([
        # 图片缩放，把所有图片缩放到同一个尺寸
        transforms.Resize((224, 224)),
        # 把PIL图片或numpy数组转换为张量
        transforms.ToTensor()
    ])

    dataset = datasets.ImageFolder(
        root=path,
        transform=transform
    )

    dataloader = DataLoader(
        dataset=dataset,
        batch_size=8,
        shuffle=True
    )

    for x, y in dataloader:
        print(x.shape)
        print(x)
        print(y)
        break


# 加载官方数据集
# 加载MNIST数据集
def test01():
    transform = transforms.Compose([
        transforms.ToTensor()
    ])

    # MNIST数据集：0-9的手写数字图片，每张图片的尺寸为28*28
    # MNIST构造函数
    # 参数：
    # root：存储数据集的本地路径
    # train：是否下载训练数据集，True-训练数据集，False-测试数据集
    # transform：转换器，可以将图片做预处理转换
    dataset = datasets.MNIST(
        root='datasets/',
        train=True,
        download=True,
        transform=transform
    )

    dataloader = DataLoader(
        dataset=dataset,
        batch_size=4,
        shuffle=True
    )

    for x, y in dataloader:
        print(x)
        print(y)
        break


if __name__ == '__main__':
    # load_csv_data()
    # load_img_data()
    test01()
