import torch
from torchvision import transforms
from PIL import Image
from matplotlib import pyplot as plt


def test01():
    path = './datasets/images/100.jpg'

    img = Image.open(path)
    print(img.size)

    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor()
    ])

    t_img = transform(img)
    print(t_img.size())

    t_img = torch.permute(t_img, (1, 2, 0))

    plt.imshow(t_img)
    plt.show()


def test02():
    path = './datasets/images/100.jpg'

    img = Image.open(path)
    print(img.size)

    transform = transforms.Compose([
        # 随机裁剪
        transforms.RandomCrop(size=(224, 224)),

        transforms.ToTensor()
    ])

    t_img = transform(img)
    print(t_img.size())

    t_img = torch.permute(t_img, (1, 2, 0))

    plt.imshow(t_img)
    plt.show()


def test03():
    path = './datasets/images/100.jpg'

    img = Image.open(path)
    print(img.size)

    transform = transforms.Compose([
        # 随机水平翻转
        transforms.RandomHorizontalFlip(p=0),
        transforms.ToTensor()
    ])

    t_img = transform(img)
    print(t_img.size())

    t_img = torch.permute(t_img, (1, 2, 0))

    plt.imshow(t_img)
    plt.show()


def test04():
    path = './datasets/images/100.jpg'

    img = Image.open(path)
    print(img.size)

    transform = transforms.Compose([
        # 随机旋转
        # degrees参数：degrees=30，表示在(-30,30)之间随机旋转，degrees=(30,60),表示在该范围内随机旋转
        transforms.RandomRotation((30, 90)),
        transforms.ToTensor()
    ])

    t_img = transform(img)
    print(t_img.size())

    t_img = torch.permute(t_img, (1, 2, 0))

    plt.imshow(t_img)
    plt.show()


def test05():
    t = torch.randn(3, 224, 224)
    transform = transforms.Compose([
        # 张量转PIL图片
        transforms.ToPILImage()
    ])

    img = transform(t)
    print(img.size)

    img.show()


def test06():
    path = './datasets/images/100.jpg'

    img = Image.open(path)
    print(img.size)
    transform = transforms.Compose([

        transforms.ToTensor(),
        # Normalize:标准化
        # mean：均值
        # std：标准差
        # 如果数据集是官方数据集，需要查看官方提供的mean和std值
        # 如果是自定义的数据集，可以将mean和std设置为[0.5, 0.5, 0.5]，是一个经验值
        # Normalize要在ToTensor后边执行，否则报错
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])

    t_img = transform(img)
    print(t_img.size())

    t_img = torch.permute(t_img, (1, 2, 0))

    plt.imshow(t_img)
    plt.show()


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    # test04()
    # test05()
    test06()
