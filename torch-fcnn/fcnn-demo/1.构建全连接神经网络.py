import torch
from torch import nn, optim


# 创建全连接神经网络步骤：
# 1.需要继承nn.Module抽象类
# 2.实现__init__方法，在该方法中定义网络结构
# 3.实现forward方法，定义网络结构的前向传播
class MyNet(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()

        self.fc1 = nn.Linear(in_features, 256)

        self.fc2 = nn.Linear(256, 128)

        self.fc3 = nn.Linear(128, out_features)

    def forward(self, x):
        x = self.fc1(x)
        x = self.fc2(x)
        x = self.fc3(x)

        return x


def test01():
    in_features = 50
    out_features = 1

    model = MyNet(in_features, out_features)

    print(model)


# nn.Sequential快速创建神经网络
# 可以自动实现forward前向传播，默认从上到下依次加载
# 注意：在Sequential中定义网络结构时需要注意网络层的顺序
def test02():
    in_features = 50
    out_features = 1
    model = nn.Sequential(
        nn.Linear(in_features, 256),
        nn.Linear(256, 128),
        nn.Linear(128, out_features)
    )
    print(model)


# 创建单层网络结构
# 直接使用Linear创建
def test03():
    in_features = 50
    out_features = 1

    model = nn.Linear(in_features, out_features)
    print(model)


# 使用神经网络基本组件完成反向传播
def test04():
    # 定义单层网络结构
    model = nn.Linear(20, 10)

    # 定义损失函数:均方误差=((y_pred - y) ** 2).mean()
    criterion = nn.MSELoss()

    # 定义优化器，整合了梯度清零、参数更新
    # 参数：
    # model.parameters()：模型参数-权重和偏置
    # lr:学习率
    opt = optim.SGD(model.parameters(), lr=0.1)

    x = torch.randn(100, 20)
    y = torch.randn(100, 10)

    epochs = 10

    for epoch in range(epochs):
        # 根据模型获取预测值
        y_pred = model(x)
        # 根据预测值和真实值计算损失
        loss = criterion(y_pred, y)

        # 梯度清零
        opt.zero_grad()

        # 反向传播
        loss.backward()

        # 梯度下降，更新模型参数
        opt.step()

        print(f'epoch:{epoch}, loss:{loss.item()}')


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    test04()
