import torch
from torch.utils.data import Dataset, DataLoader, TensorDataset

from sklearn.datasets import make_regression
from torch import nn, optim


# 自定义数据集类步骤：
# 1.继承Dataset类
# 2.实现__init__方法，初始化外部的数据
# 3.实现__len__方法，用来返回数据集的长度
# 4.实现__getitem__方法，根据索引获取对应位置的数据
class MyDataset(Dataset):
    def __init__(self, data, labels):
        self.data = data
        self.labels = labels

    def __len__(self):
        return len(self.data)

    def __getitem__(self, index):
        sample = self.data[index]
        label = self.labels[index]

        return sample, label


def test01():
    x = torch.randn(100, 20)
    y = torch.randn(100, 1)

    dataset = MyDataset(x, y)
    print(dataset[0])

    # DataLoader:数据加载器，用来分批次加载数据，返回一个迭代器
    # 参数：
    # batch_size:设置每批次加载的样本数量
    # shuffle：设置是否要打乱数据，True-打乱，False-不打乱
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=20,
        shuffle=True
    )

    for sample, label in dataloader:
        print(sample)
        print(label)
        break


# TensorDataset:pytorch提供的一个Dataset类
# 优先选择TensorDataset，如果该类不能满足需求，再考虑自定义Dataset
def test02():
    x = torch.randn(100, 20)
    y = torch.randn(100, 1)

    dataset = TensorDataset(x, y)

    dataloader = DataLoader(
        dataset=dataset,
        batch_size=20,
        shuffle=True
    )

    for sample, label in dataloader:
        print(sample)
        print(label)
        break


# pytorch实现线性回归
def build_data(in_features, out_features):
    bias = 14.5

    # 生成的数据需要转换为tensor
    x, y, coef = make_regression(
        n_samples=1000,
        n_features=in_features,
        n_targets=out_features,
        coef=True,
        bias=bias,
        noise=0.1,
        random_state=42
    )

    x = torch.tensor(x, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.float32).view(-1, out_features)
    coef = torch.tensor(coef, dtype=torch.float32)
    bias = torch.tensor(bias, dtype=torch.float32)

    return x, y, coef, bias


def train():
    # 数据准备
    in_features = 10
    out_features = 1
    x, y, coef, bias = build_data(in_features, out_features)

    dataset = TensorDataset(x, y)

    dataloader = DataLoader(
        dataset=dataset,
        batch_size=100,
        shuffle=True
    )

    # 定义网络模型
    model = nn.Linear(in_features, out_features)

    # 损失函数
    criterion = nn.MSELoss()

    # 优化器
    opt = optim.SGD(model.parameters(), lr=0.1)

    epochs = 20

    for epoch in range(epochs):
        for tx, ty in dataloader:
            y_pred = model(tx)

            loss = criterion(y_pred, ty)

            opt.zero_grad()

            loss.backward()

            opt.step()

        print(f'epoch:{epoch},loss:{loss.item()}')

    # detach()、data:作用是将计算图中的weight参数值获取出来
    print(f'真实权重：{coef.numpy()}, 训练权重：{model.weight.data.numpy()}')
    print(f'真实偏置：{bias},训练偏置：{model.bias.item()}')




if __name__ == '__main__':
    # test01()
    # test02()
    train()
