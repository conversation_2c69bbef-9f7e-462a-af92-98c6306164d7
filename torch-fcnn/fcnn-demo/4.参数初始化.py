import torch
from torch import nn

from torch.nn import functional as F


# 随机初始化
def test01():
    model = nn.Linear(8, 1)

    print(model.weight)
    # 均匀分布初始化
    # nn.init.uniform_(model.weight)

    # 正态分布初始化
    # 参数：
    # mean：均值
    # std：标准差
    nn.init.normal_(model.weight, mean=0, std=1)

    print(model.weight)


# xavier初始化
# 原理：
# 1.前向传播的方差保持一致
# 2.反向传播的梯度方差保持一致
def test02():
    model = nn.Linear(8, 1)
    # 均匀分布初始化
    # nn.init.xavier_uniform_(model.weight)

    # 正态分布初始化
    nn.init.xavier_normal_(model.weight)

    print(model.weight)


# He初始化(kaiming初始化)
# pytorch框架默认使用He初始化
def test03():
    model = nn.Linear(8, 1)
    # 均匀分布初始化
    # mode: 模式：fan_in-优先保证前向传播方差一致性，fan_out-优先保证反向传播方差一致性
    # nn.init.kaiming_uniform_(model.weight, mode='fan_in')

    # 正态分布初始化
    nn.init.kaiming_normal_(model.weight, mode='fan_out')

    print(model.weight)


# 构建带有激活函数的神经网络，并做参数初始化
class MyNet(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()
        self.fc1 = nn.Linear(in_features, 64)
        nn.init.kaiming_uniform_(self.fc1.weight)
        self.relu1 = nn.ReLU()

        self.fc2 = nn.Linear(64, 32)
        nn.init.kaiming_uniform_(self.fc2.weight)
        self.relu2 = nn.ReLU()

        self.fc3 = nn.Linear(32, out_features)
        nn.init.kaiming_uniform_(self.fc3.weight)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.relu1(self.fc1(x))
        x = self.relu2(self.fc2(x))
        x = self.sigmoid(self.fc3(x))
        return x


class MyNet1(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()
        self.fc1 = nn.Linear(in_features, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, out_features)

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = F.sigmoid(self.fc3(x))

        return x


def test04():
    model = MyNet(10, 2)
    print(model)


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    test04()
