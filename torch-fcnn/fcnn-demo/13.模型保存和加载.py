import torch
from torch import nn


class MyNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc1 = nn.Linear(10, 64)
        self.fc2 = nn.Linear(64, 5)

    def forward(self, x):
        x = self.fc1(x)
        x = self.fc2(x)
        return x


# 保存模型
def test01():
    model = MyNet()
    print(model)
    torch.save(model, './model/fcnn_model.pt')


# 加载模型
# 得到的是完整的模型对象
def test02():
    model = torch.load('./model/fcnn_model.pt')
    print(model)


# 保存模型参数
def test03():
    model = MyNet()
    state_dict = model.state_dict()
    torch.save(state_dict, './model/fcnn_state_dict.pt')


# 加载模型参数
# 如果保存的是模型参数，加载的模型数据是字典，内容是模型参数，并不是完整的模型
# 需要事先初始化模型，然后把模型参数导入到模型中
def test04():
    model = MyNet()
    state_dict = torch.load('./model/fcnn_state_dict.pt')
    model.load_state_dict(state_dict)

    print(model)


if __name__ == '__main__':
    # test01()
    # test02()
    # test03()
    test04()