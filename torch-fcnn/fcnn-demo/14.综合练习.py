# 使用全连接网络训练和预测MNIST数据集
# 1.数据准备：通过数据加载器加载官方MNIST数据集
# 2.构建网络结构
# 3.实现训练方法：使用交叉熵损失函数、Adam优化器
# 4.实现验证方法
# 5.通过测试图片进行预测

import torch
from torch import nn, optim
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
from PIL import Image


# 数据准备
def build_data():
    transform = transforms.Compose(
        [
            transforms.Resize((28, 28)),
            transforms.ToTensor(),
        ]
    )
    # 训练数据集
    train_dataset = datasets.MNIST(
        root='./datasets',
        train=True,
        download=True,
        transform=transform
    )
    # 验证数据集
    val_dataset = datasets.MNIST(
        root='./datasets',
        train=False,
        download=True,
        transform=transform
    )

    # 训练数据加载器
    train_dataloader = DataLoader(
        dataset=train_dataset,
        batch_size=64,
        shuffle=True
    )

    # 验证数据加载器
    val_dataloader = DataLoader(
        dataset=val_dataset,
        batch_size=64,
        shuffle=True
    )

    return train_dataloader, val_dataloader


# 构建网络结构
class MNISTNet(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()
        self.fc1 = nn.Linear(in_features, 128)
        self.bn1 = nn.BatchNorm1d(128)
        self.relu1 = nn.ReLU()

        self.fc2 = nn.Linear(128, 64)
        self.bn2 = nn.BatchNorm1d(64)
        self.relu2 = nn.ReLU()

        self.fc3 = nn.Linear(64, out_features)

    def forward(self, x):
        x = x.view(-1, 1 * 28 * 28)
        x = self.relu1(self.bn1(self.fc1(x)))
        x = self.relu2(self.bn2(self.fc2(x)))
        x = self.fc3(x)
        return x


# 训练
def train(model, train_dataloader, lr, epochs):
    model.train()

    criterion = nn.CrossEntropyLoss()
    opt = optim.Adam(model.parameters(), lr=lr, betas=(0.9, 0.999), eps=1e-8, weight_decay=0.001)

    for epoch in range(epochs):
        correct = 0
        for tx, ty in train_dataloader:
            y_pred = model(tx)
            loss = criterion(y_pred, ty)
            opt.zero_grad()
            loss.backward()
            opt.step()

            _, pred = torch.max(y_pred.data, dim=1)
            correct += (pred == ty).sum().item()
        acc = correct / len(train_dataloader.dataset)

        print(f'epoch:{epoch},loss:{loss.item():.4f},acc:{acc:.4f}')


def eval(model, val_dataloader):
    model.eval()

    criterion = nn.CrossEntropyLoss()
    correct = 0
    for vx, vy in val_dataloader:
        with torch.no_grad():
            y_pred = model(vx)
            loss = criterion(y_pred, vy)
            _, pred = torch.max(y_pred, dim=1)
            correct += (pred == vy).sum().item()

    acc = correct / len(val_dataloader.dataset)
    print(f'loss:{loss.item()},acc:{acc}')


def save_model(model, path):
    torch.save(model.state_dict(), path)


def load_model(path):
    model = MNISTNet(1 * 28 * 28, 10)
    model.load_state_dict(torch.load(path))

    return model


def predict(test_path, model_path):
    transform = transforms.Compose([
        transforms.Resize((28, 28)),
        transforms.ToTensor()
    ])

    img = Image.open(test_path).convert('L')
    t_img = transform(img).unsqueeze(0)

    model = load_model(model_path)

    model.eval()
    with torch.no_grad():
        y_pred = model(t_img)
        _, pred = torch.max(y_pred, dim=1)

        print(f'预测分类：{pred.item()}')


if __name__ == '__main__':
    # train_dataloader, val_dataloader = build_data()
    # model = MNISTNet(1 * 28 * 28, 10)
    # train(model, train_dataloader, lr=0.01, epochs=20)
    # eval(model, val_dataloader)
    # save_model(model, './model/mnist_model.pt')

    predict('./datasets/images/5.png', './model/mnist_model.pt')
