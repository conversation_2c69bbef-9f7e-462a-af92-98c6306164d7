import torch
from torch import nn, optim
from sklearn.datasets import make_circles
from sklearn.model_selection import train_test_split
from matplotlib import pyplot as plt


# 同心圆数据
def build_data():
    x, y = make_circles(
        n_samples=2000,
        factor=0.4,
        noise=0.1,
        random_state=42
    )

    print(x[0])
    print(y[0:5])

    x = torch.tensor(x, dtype=torch.float)
    y = torch.tensor(y, dtype=torch.long)

    # plt.scatter(x[:, 0], x[:, 1], c=y)
    # plt.show()

    x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=42)

    return x_train, x_test, y_train, y_test


# 构建网络模型,带批量标准化
class NetWithBN(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()

        self.fc1 = nn.Linear(in_features, 128)
        self.bn1 = nn.BatchNorm1d(128)
        self.relu1 = nn.ReLU()

        self.fc2 = nn.Linear(128, 64)
        self.bn2 = nn.BatchNorm1d(64)
        self.relu2 = nn.ReLU()

        self.fc3 = nn.Linear(64, out_features)

    def forward(self, x):
        x = self.relu1(self.bn1(self.fc1(x)))
        x = self.relu2(self.bn2(self.fc2(x)))
        x = self.fc3(x)

        return x


# 创建网络模型，不使用批量标准化
class NetWithoutBN(nn.Module):
    def __init__(self, in_features, out_features):
        super().__init__()

        self.fc1 = nn.Linear(in_features, 128)
        self.relu1 = nn.ReLU()

        self.fc2 = nn.Linear(128, 64)
        self.relu2 = nn.ReLU()

        self.fc3 = nn.Linear(64, out_features)

    def forward(self, x):
        x = self.relu1(self.fc1(x))
        x = self.relu2(self.fc2(x))
        x = self.fc3(x)

        return x


def train(model, x_train, y_train, epochs):
    # 如果网络模型中使用了dropout或批量标准化，train()默认启动dropout或批量标准化的功能
    model.train()

    criterion = nn.CrossEntropyLoss()

    opt = optim.Adam(model.parameters(), lr=0.1, betas=(0.9, 0.999))

    loss_list = []

    for epoch in range(epochs):
        y_pred = model(x_train)
        print(y_pred[0])
        print(y_train.shape)
        loss = criterion(y_pred, y_train)

        opt.zero_grad()

        loss.backward()

        opt.step()

        loss_list.append(loss.item())

    return loss_list


def eval(model, x_test, y_test, epochs):
    # 验证阶段会自动关闭dropout或批量标准化的参数更新
    model.eval()

    acc_list = []
    for epoch in range(epochs):
        with torch.no_grad():
            y_pred = model(x_test)
            _, pred = torch.max(y_pred, dim=1)

            correct = (pred == y_test).sum().item()

            acc = correct / len(y_test)

            acc_list.append(acc)

    return acc_list


def plot(bn_loss_list, no_bn_loss_list, bn_acc_list, no_bn_acc_list):
    fig = plt.figure(figsize=(12, 5))
    ax1 = fig.add_subplot(1, 2, 1)
    ax1.plot(bn_loss_list, 'b', label='BN')
    ax1.plot(no_bn_loss_list, 'r', label='NoBN')
    ax1.legend()

    ax2 = fig.add_subplot(1, 2, 2)
    ax2.plot(bn_acc_list, 'b', label='BN')
    ax2.plot(no_bn_acc_list, 'r', label='NoBN')
    ax2.legend()
    plt.show()


if __name__ == '__main__':
    x_train, x_test, y_train, y_test = build_data()
    bn_model = NetWithBN(2, 2)
    nobn_model = NetWithoutBN(2, 2)

    bn_loss_list = train(bn_model, x_train, y_train, 30)
    no_bn_loss_list = train(nobn_model, x_train, y_train, 30)

    bn_acc_list = eval(bn_model, x_test, y_test, 30)
    no_bn_acc_list = eval(nobn_model, x_test, y_test, 30)

    plot(bn_loss_list, no_bn_loss_list, bn_acc_list, no_bn_acc_list)
