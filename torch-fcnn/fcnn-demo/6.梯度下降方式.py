import torch
from torch.utils.data import TensorDataset, DataLoader
from torch import nn, optim


def test01():
    model = nn.Linear(10, 5)
    x = torch.randn(10000, 10)
    y = torch.rand(10000, 5)
    criterion = nn.MSELoss()
    # momentum:动量，作用：根据历史梯度增加惯性
    # 参数值：动量系数，一般取0.9
    opt = optim.SGD(model.parameters(), lr=0.1, momentum=0.9)

    dataset = TensorDataset(x, y)
    # 批量梯度下降
    # dataloader = DataLoader(
    #     dataset=dataset,
    #     batch_size=len(dataset),
    #     shuffle=True
    # )

    # 随机梯度下降，随机选择一条样本进行梯度更新
    # dataloader = DataLoader(
    #     dataset=dataset,
    #     batch_size=1,
    #     shuffle=True
    # )

    # 小批量梯度下降，常用
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=256,
        shuffle=True
    )

    epochs = 200

    for epoch in range(epochs):
        for tx, ty in dataloader:
            y_pred = model(tx)
            loss = criterion(y_pred, ty)

            opt.zero_grad()
            loss.backward()
            opt.step()

        print(f'epoch:{epoch}, loss:{loss.item()}')


def test02():
    model = nn.Linear(10, 5)
    x = torch.randn(1000, 10)
    y = torch.randn(1000, 5)

    criterion = nn.MSELoss()

    # Adagrad: 自适应学习率优化器
    # 原理：历史梯度平方和作为学习率的分母，动态调整学习率
    # 优点：自适应动态调整学习率
    # 缺点：随着训练时间增加，历史梯度平方和越来越大，导致学习率越来越小，可能会停止参数更新
    # eps: 避免学习率的分母为0，是一个非常小的数字
    # opt = optim.Adagrad(model.parameters(), lr=0.1, eps=1e-8)

    # RMSprop：自适应学习率优化器
    # 原理：使用指数加权平均对历史梯度平方求和，将平方和作为分母调整学习率
    # 优点：缓解历史梯度平方和快速变大，使学习率衰减更加平稳
    # 缺点：需要调整alpha和lr参数，找到最优值
    # opt = optim.RMSprop(model.parameters(), lr=0.1, alpha=0.9, eps=1e-8)

    # Adam：自适应优化器
    # 结合了动量和RMSprop，既优化了梯度，又能动态调整学习率
    # 缺点是对参数设置比较敏感，需要根据实际情况进行调整
    # betas参数：是一个元组，第一个元素是一阶动量的系数0.9，第二个元素是二阶动量的系数0.999，两个系数是经验值
    opt = optim.Adam(model.parameters(), lr=0.1, betas=(0.9, 0.999), eps=1e-8)

    for epoch in range(50):
        y_pred = model(x)
        loss = criterion(y_pred, y)

        opt.zero_grad()

        loss.backward()

        opt.step()

        print(f'loss:{loss.item()}')


if __name__ == '__main__':
    # test01()
    test02()
